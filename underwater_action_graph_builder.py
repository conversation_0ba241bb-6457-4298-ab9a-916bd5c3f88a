"""
Isaac Sim 4.5 Underwater Action Graph Builder
Pure Python implementation using correct OmniGraph Controller API
"""

import omni.graph.core as og
import omni.kit.commands
import carb


class UnderwaterActionGraphBuilder:
    """Build underwater simulation Action Graph using Isaac Sim 4.5 API"""
    
    def __init__(self, graph_path="/UnderwaterActionGraph"):
        self.graph_path = graph_path
        self.graph_handle = None
        self.nodes = {}
        
    def create_basic_test_graph(self):
        """Create a basic test graph to verify API functionality"""
        carb.log_info("Creating basic test Action Graph...")
        
        # Delete existing graph
        try:
            omni.kit.commands.execute('DeletePrims', paths=[self.graph_path])
        except:
            pass
        
        try:
            keys = og.Controller.Keys
            (self.graph_handle, nodes_created, _, _) = og.Controller.edit(
                {"graph_path": self.graph_path, "evaluator_name": "execution"},
                {
                    keys.CREATE_NODES: [
                        ("tick", "omni.graph.action.OnTick"),
                        ("print", "omni.graph.ui_nodes.PrintText")
                    ],
                    keys.SET_VALUES: [
                        ("print.inputs:text", "Underwater Action Graph Test"),
                        ("print.inputs:logLevel", "Warning")
                    ],
                    keys.CONNECT: [
                        ("tick.outputs:tick", "print.inputs:execIn")
                    ],
                },
            )
            
            carb.log_info("Basic test graph created successfully")
            return True
            
        except Exception as e:
            carb.log_error(f"Failed to create basic test graph: {e}")
            return False
    
    def create_underwater_simulation_graph(self, rov_prim_path="/World/ROV"):
        """Create complete underwater simulation Action Graph"""
        carb.log_info("Creating underwater simulation Action Graph...")
        
        # Delete existing graph
        try:
            omni.kit.commands.execute('DeletePrims', paths=[self.graph_path])
        except:
            pass
        
        try:
            keys = og.Controller.Keys
            (self.graph_handle, nodes_created, _, _) = og.Controller.edit(
                {"graph_path": self.graph_path, "evaluator_name": "execution"},
                {
                    keys.CREATE_NODES: [
                        # Execution trigger
                        ("tick", "omni.graph.action.OnTick"),

                        # Input nodes
                        ("get_transform", "omni.graph.nodes.GetPrimLocalToWorldTransform"),
                        # Note: Gamepad input will be added separately if available

                        # Constants for ROV properties
                        ("volume_const", "omni.graph.nodes.ConstantDouble"),
                        ("height_const", "omni.graph.nodes.ConstantDouble"),
                        ("max_damping_const", "omni.graph.nodes.ConstantDouble"),

                        # Math nodes
                        ("extract_z", "omni.graph.nodes.ExtractComponent"),
                        ("make_force_vector", "omni.graph.nodes.MakeArray"),

                        # Script nodes for physics calculations
                        ("quat_to_euler_script", "omni.graph.scriptnode.ScriptNode"),
                        ("buoyancy_script", "omni.graph.scriptnode.ScriptNode"),
                        ("damping_script", "omni.graph.scriptnode.ScriptNode"),
                        ("controller_script", "omni.graph.scriptnode.ScriptNode"),
                        ("thruster_script", "omni.graph.scriptnode.ScriptNode"),

                        # Output nodes
                        ("apply_force", "omni.graph.nodes.ApplyForce"),
                        ("debug_print", "omni.graph.ui_nodes.PrintText"),
                    ],
                    keys.SET_VALUES: [
                        # Configure input nodes
                        ("get_transform.inputs:prim", rov_prim_path),

                        # Set ROV physical properties
                        ("volume_const.inputs:value", 8.0),  # 2x2x2 cube volume
                        ("height_const.inputs:value", 2.0),  # ROV height
                        ("max_damping_const.inputs:value", 10.0),  # Max damping coefficient

                        # Configure math nodes
                        ("extract_z.inputs:index", 2),  # Extract Z component
                        ("make_force_vector.inputs:arraySize", 3),  # 3D force vector

                        # Configure output nodes
                        ("apply_force.inputs:prim", rov_prim_path),
                        ("debug_print.inputs:text", "Underwater Sim Running"),
                        ("debug_print.inputs:logLevel", "Info"),

                        # Set script contents
                        ("quat_to_euler_script.inputs:script", self.get_quat_to_euler_script()),
                        ("buoyancy_script.inputs:script", self.get_buoyancy_script()),
                        ("damping_script.inputs:script", self.get_damping_script()),
                        ("controller_script.inputs:script", self.get_controller_script()),
                        ("thruster_script.inputs:script", self.get_thruster_script()),
                    ],
                    keys.CONNECT: [
                        # Execution flow
                        ("tick.outputs:tick", "get_transform.inputs:execIn"),
                        ("get_transform.outputs:execOut", "debug_print.inputs:execIn"),
                        
                        # Transform data flow
                        ("get_transform.outputs:rotation", "quat_to_euler_script.inputs:quaternion"),
                        ("get_transform.outputs:translation", "extract_z.inputs:vector"),
                        
                        # Physics calculations
                        ("extract_z.outputs:component", "buoyancy_script.inputs:z_position"),
                        ("volume_const.outputs:value", "buoyancy_script.inputs:volume"),
                        ("height_const.outputs:value", "buoyancy_script.inputs:height"),
                        ("quat_to_euler_script.outputs:rotation", "buoyancy_script.inputs:rotation"),
                        
                        # Damping calculations
                        ("extract_z.outputs:component", "damping_script.inputs:z_position"),
                        ("max_damping_const.outputs:value", "damping_script.inputs:max_damping"),
                        ("height_const.outputs:value", "damping_script.inputs:floating_obj_height"),
                        
                        # Control system
                        ("quat_to_euler_script.outputs:rotation", "controller_script.inputs:orientation"),
                        # Note: Gamepad connections will be added manually in Action Graph editor
                        
                        # Force application
                        ("buoyancy_script.outputs:force_vector", "apply_force.inputs:force"),
                    ],
                },
            )
            
            carb.log_info("Underwater simulation Action Graph created successfully")
            return True
            
        except Exception as e:
            carb.log_error(f"Failed to create underwater simulation graph: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_quat_to_euler_script(self):
        """Get quaternion to euler conversion script"""
        return '''
import math

def setup(db):
    pass

def compute(db):
    # Get quaternion input [x, y, z, w]
    q = db.inputs.quaternion
    if len(q) != 4:
        db.outputs.rotation = [0.0, 0.0, 0.0]
        return
    
    x, y, z, w = q
    
    # Convert to Euler angles (roll, pitch, yaw)
    # Roll (x-axis rotation)
    sinr_cosp = 2 * (w * x + y * z)
    cosr_cosp = 1 - 2 * (x * x + y * y)
    roll = math.atan2(sinr_cosp, cosr_cosp)
    
    # Pitch (y-axis rotation)
    sinp = 2 * (w * y - z * x)
    if abs(sinp) >= 1:
        pitch = math.copysign(math.pi / 2, sinp)
    else:
        pitch = math.asin(sinp)
    
    # Yaw (z-axis rotation)
    siny_cosp = 2 * (w * z + x * y)
    cosy_cosp = 1 - 2 * (y * y + z * z)
    yaw = math.atan2(siny_cosp, cosy_cosp)
    
    # Convert to degrees
    db.outputs.rotation = [
        math.degrees(roll),
        math.degrees(pitch),
        math.degrees(yaw)
    ]
'''
    
    def get_buoyancy_script(self):
        """Get buoyancy forces calculation script"""
        return '''
import math

def setup(db):
    db.water_density = 1000.0  # kg/m³
    db.gravity = 9.8  # m/s²

def compute(db):
    volume = db.inputs.volume
    height = db.inputs.height
    z_pos = db.inputs.z_position
    rotation = db.inputs.rotation
    
    if z_pos >= 0:
        # Above water
        db.outputs.x_force = 0.0
        db.outputs.y_force = 0.0
        db.outputs.z_force = 0.0
        db.outputs.force_vector = [0.0, 0.0, 0.0]
        return
    
    # Calculate submerged volume
    submerged_height = min(height, abs(z_pos))
    submerged_volume = volume * (submerged_height / height)
    
    # Basic buoyancy force (upward)
    buoyancy_force = db.water_density * submerged_volume * db.gravity
    
    # Apply rotation effects
    if len(rotation) >= 3:
        roll, pitch, yaw = rotation
        roll_rad = math.radians(roll)
        pitch_rad = math.radians(pitch)
        
        # Distribute force based on rotation
        x_force = buoyancy_force * math.sin(pitch_rad) * 0.1
        y_force = buoyancy_force * math.sin(roll_rad) * 0.1
        z_force = buoyancy_force * math.cos(roll_rad) * math.cos(pitch_rad)
    else:
        x_force = 0.0
        y_force = 0.0
        z_force = buoyancy_force
    
    db.outputs.x_force = x_force
    db.outputs.y_force = y_force
    db.outputs.z_force = z_force
    db.outputs.force_vector = [x_force, y_force, z_force]
'''
    
    def get_damping_script(self):
        """Get damping calculation script"""
        return '''
def setup(db):
    pass

def compute(db):
    z_pos = db.inputs.z_position
    max_damping = db.inputs.max_damping
    obj_height = db.inputs.floating_obj_height
    
    if z_pos >= 0:
        # Above water - no damping
        db.outputs.linear_damping = 0.0
        db.outputs.angular_damping = 0.0
    else:
        # Calculate damping based on submersion
        submerged_ratio = min(1.0, abs(z_pos) / obj_height)
        
        # Linear and angular damping increase with submersion
        db.outputs.linear_damping = max_damping * submerged_ratio
        db.outputs.angular_damping = max_damping * submerged_ratio * 0.5
'''
    
    def get_controller_script(self):
        """Get PID controller script"""
        return '''
def setup(db):
    db.kp = 100.0
    db.ki = 10.0
    db.kd = 0.01
    db.integral = 0.0
    db.previous_error = 0.0
    db.saturation_limit = 1000.0

def compute(db):
    current_orientation = db.inputs.orientation
    dive_force = getattr(db.inputs, 'dive_force', 0.0)
    
    if len(current_orientation) >= 3:
        # Simple roll stabilization
        target_roll = 0.0
        current_roll = current_orientation[0]
        error = target_roll - current_roll
        
        # PID calculation
        db.integral += error
        derivative = error - db.previous_error
        
        output = (db.kp * error + 
                 db.ki * db.integral + 
                 db.kd * derivative)
        
        # Apply saturation
        if output > db.saturation_limit:
            output = db.saturation_limit
        elif output < -db.saturation_limit:
            output = -db.saturation_limit
        
        db.previous_error = error
        
        # Output forces
        db.outputs.force = [output, 0.0, dive_force]
        db.outputs.minus_force = [-output, 0.0, -dive_force]
    else:
        db.outputs.force = [0.0, 0.0, 0.0]
        db.outputs.minus_force = [0.0, 0.0, 0.0]
'''
    
    def get_thruster_script(self):
        """Get thruster control script"""
        return '''
def setup(db):
    db.max_thrust = 500.0

def compute(db):
    y_stick = db.inputs.y_stick
    x_stick = db.inputs.x_stick
    
    # Calculate individual thruster forces
    # Forward/backward movement
    forward_thrust = y_stick * db.max_thrust
    
    # Left/right turning
    turn_thrust = x_stick * db.max_thrust * 0.5
    
    # Distribute to four thrusters
    db.outputs.left_front = [0.0, forward_thrust + turn_thrust, 0.0]
    db.outputs.right_front = [0.0, forward_thrust - turn_thrust, 0.0]
    db.outputs.left_back = [0.0, forward_thrust + turn_thrust, 0.0]
    db.outputs.right_back = [0.0, forward_thrust - turn_thrust, 0.0]
'''


def main():
    """Main function to create underwater Action Graph"""
    builder = UnderwaterActionGraphBuilder()
    
    print("=" * 60)
    print("Isaac Sim 4.5 Underwater Action Graph Builder")
    print("=" * 60)
    
    # First test basic functionality
    print("\n1. Testing basic Action Graph creation...")
    if builder.create_basic_test_graph():
        print("✓ Basic test graph created successfully")
    else:
        print("✗ Basic test failed")
        return
    
    # Create full underwater simulation
    print("\n2. Creating underwater simulation graph...")
    if builder.create_underwater_simulation_graph("/World/ROV"):
        print("✓ Underwater simulation graph created successfully")
    else:
        print("✗ Underwater simulation creation failed")
        return
    
    print("\n" + "=" * 60)
    print("SUCCESS: Action Graph created!")
    print("=" * 60)
    print("Next steps:")
    print("1. Open Window → Visual Scripting → Action Graph")
    print("2. Select /UnderwaterActionGraph")
    print("3. Create ROV at /World/ROV")
    print("4. Click Play to start simulation")
    print("5. Connect gamepad for control")


if __name__ == "__main__":
    main()
