"""
Simple API Test for Isaac Sim 4.5
Test the correct OmniGraph Controller API
"""

import omni.graph.core as og
import omni.kit.commands
import carb


def test_basic_api():
    """Test basic API functionality"""
    graph_path = "/APITestGraph"
    
    print("Testing Isaac Sim 4.5 OmniGraph API...")
    
    # Delete existing graph
    try:
        omni.kit.commands.execute('DeletePrims', paths=[graph_path])
        print("✓ Deleted existing graph")
    except:
        print("- No existing graph to delete")
    
    try:
        # Test the exact API pattern from the example
        keys = og.Controller.Keys
        (graph_handle, list_of_nodes, _, _) = og.Controller.edit(
            {"graph_path": graph_path, "evaluator_name": "execution"},
            {
                keys.CREATE_NODES: [
                    ("tick", "omni.graph.action.OnTick"),
                    ("print", "omni.graph.ui_nodes.PrintText")
                ],
                keys.SET_VALUES: [
                    ("print.inputs:text", "API Test Success"),
                    ("print.inputs:logLevel", "Warning")
                ],
                keys.CONNECT: [
                    ("tick.outputs:tick", "print.inputs:execIn")
                ],
            },
        )
        
        print(f"✓ Graph created: {graph_handle}")
        print(f"✓ Nodes created: {len(list_of_nodes)}")
        
        return True
        
    except Exception as e:
        print(f"✗ API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_script_node():
    """Test Python script node creation"""
    graph_path = "/ScriptTestGraph"
    
    print("\nTesting script node creation...")
    
    # Delete existing graph
    try:
        omni.kit.commands.execute('DeletePrims', paths=[graph_path])
    except:
        pass
    
    try:
        keys = og.Controller.Keys
        (graph_handle, list_of_nodes, _, _) = og.Controller.edit(
            {"graph_path": graph_path, "evaluator_name": "execution"},
            {
                keys.CREATE_NODES: [
                    ("tick", "omni.graph.action.OnTick"),
                    ("script", "omni.graph.scriptnode.ScriptNode"),
                    ("print", "omni.graph.ui_nodes.PrintText")
                ],
                keys.SET_VALUES: [
                    ("script.inputs:script", '''
def setup(db):
    db.counter = 0
    print("Script node initialized")

def compute(db):
    db.counter += 1
    db.outputs.result = db.counter * 10
    if db.counter <= 3:
        print(f"Script execution #{db.counter}, result: {db.outputs.result}")
'''),
                    ("print.inputs:text", "Script Test"),
                    ("print.inputs:logLevel", "Info")
                ],
                keys.CONNECT: [
                    ("tick.outputs:tick", "script.inputs:execIn"),
                    ("script.outputs:execOut", "print.inputs:execIn")
                ],
            },
        )
        
        print(f"✓ Script graph created: {graph_handle}")
        print(f"✓ Script nodes created: {len(list_of_nodes)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Script test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    print("=" * 50)
    print("Isaac Sim 4.5 API Test")
    print("=" * 50)
    
    # Test basic API
    if test_basic_api():
        print("✓ Basic API test passed")
    else:
        print("✗ Basic API test failed")
        return False
    
    # Test script node
    if test_script_node():
        print("✓ Script node test passed")
    else:
        print("✗ Script node test failed")
        return False
    
    print("\n" + "=" * 50)
    print("All tests passed!")
    print("=" * 50)
    print("Next steps:")
    print("1. Open Window → Visual Scripting → Action Graph")
    print("2. Select /APITestGraph or /ScriptTestGraph")
    print("3. Click Play to see execution")
    print("4. Run the underwater simulation builder:")
    print("   exec(open('underwater_action_graph_builder.py').read())")
    
    return True


if __name__ == "__main__":
    main()
