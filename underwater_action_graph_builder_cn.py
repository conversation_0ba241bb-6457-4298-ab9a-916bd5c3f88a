"""
Isaac Sim 4.5 水下仿真Action Graph构建器
使用正确的OmniGraph Controller API的纯Python实现
"""

import omni.graph.core as og
import omni.kit.commands
import carb


class UnderwaterActionGraphBuilder:
    """使用Isaac Sim 4.5 API构建水下仿真Action Graph"""
    
    def __init__(self, graph_path="/UnderwaterActionGraph"):
        """
        初始化Action Graph构建器
        
        Args:
            graph_path: Action Graph在场景中的路径
        """
        self.graph_path = graph_path
        self.graph_handle = None  # 图句柄
        self.nodes = {}  # 节点字典
        
    def create_basic_test_graph(self):
        """创建基础测试图以验证API功能"""
        carb.log_info("正在创建基础测试Action Graph...")
        
        # 删除已存在的图
        try:
            omni.kit.commands.execute('DeletePrims', paths=[self.graph_path])
        except:
            pass
        
        try:
            keys = og.Controller.Keys
            # 使用正确的API创建图和节点
            (self.graph_handle, nodes_created, _, _) = og.Controller.edit(
                {"graph_path": self.graph_path, "evaluator_name": "execution"},
                {
                    keys.CREATE_NODES: [
                        ("tick", "omni.graph.action.OnTick"),  # 执行触发器
                        ("print", "omni.graph.ui_nodes.PrintText")  # 打印节点
                    ],
                    keys.SET_VALUES: [
                        ("print.inputs:text", "水下Action Graph测试"),  # 设置打印文本
                        ("print.inputs:logLevel", "Warning")  # 设置日志级别为警告以便在终端看到输出
                    ],
                    keys.CONNECT: [
                        ("tick.outputs:tick", "print.inputs:execIn")  # 连接执行流
                    ],
                },
            )
            
            carb.log_info("基础测试图创建成功")
            return True
            
        except Exception as e:
            carb.log_error(f"创建基础测试图失败: {e}")
            return False
    
    def create_underwater_simulation_graph(self, rov_prim_path="/World/ROV"):
        """创建完整的水下仿真Action Graph"""
        carb.log_info("正在创建水下仿真Action Graph...")
        
        # 删除已存在的图
        try:
            omni.kit.commands.execute('DeletePrims', paths=[self.graph_path])
        except:
            pass
        
        try:
            keys = og.Controller.Keys
            # 创建完整的水下仿真图
            (self.graph_handle, nodes_created, _, _) = og.Controller.edit(
                {"graph_path": self.graph_path, "evaluator_name": "execution"},
                {
                    keys.CREATE_NODES: [
                        # 执行触发器
                        ("tick", "omni.graph.action.OnTick"),
                        
                        # 输入节点
                        ("get_transform", "omni.graph.nodes.GetPrimLocalToWorldTransform"),  # 获取物体变换
                        ("gamepad", "omni.graph.nodes.GamepadInput"),  # 游戏手柄输入
                        
                        # ROV属性常量
                        ("volume_const", "omni.graph.nodes.ConstantDouble"),  # 体积常量
                        ("height_const", "omni.graph.nodes.ConstantDouble"),  # 高度常量
                        ("max_damping_const", "omni.graph.nodes.ConstantDouble"),  # 最大阻尼常量
                        
                        # 数学运算节点
                        ("extract_z", "omni.graph.nodes.ExtractComponent"),  # 提取Z分量
                        ("make_force_vector", "omni.graph.nodes.MakeArray"),  # 创建力向量数组
                        
                        # 物理计算脚本节点
                        ("quat_to_euler_script", "omni.graph.scriptnode.ScriptNode"),  # 四元数转欧拉角
                        ("buoyancy_script", "omni.graph.scriptnode.ScriptNode"),  # 浮力计算
                        ("damping_script", "omni.graph.scriptnode.ScriptNode"),  # 阻尼计算
                        ("controller_script", "omni.graph.scriptnode.ScriptNode"),  # PID控制器
                        ("thruster_script", "omni.graph.scriptnode.ScriptNode"),  # 推进器控制
                        
                        # 输出节点
                        ("apply_force", "omni.graph.nodes.ApplyForce"),  # 施加力
                        ("debug_print", "omni.graph.ui_nodes.PrintText"),  # 调试打印
                    ],
                    keys.SET_VALUES: [
                        # 配置输入节点
                        ("get_transform.inputs:prim", rov_prim_path),  # 设置目标物体路径
                        ("gamepad.inputs:gamepadIndex", 0),  # 设置游戏手柄索引
                        
                        # 设置ROV物理属性
                        ("volume_const.inputs:value", 8.0),  # 2x2x2立方体体积
                        ("height_const.inputs:value", 2.0),  # ROV高度
                        ("max_damping_const.inputs:value", 10.0),  # 最大阻尼系数
                        
                        # 配置数学节点
                        ("extract_z.inputs:index", 2),  # 提取Z分量（索引2）
                        ("make_force_vector.inputs:arraySize", 3),  # 3D力向量
                        
                        # 配置输出节点
                        ("apply_force.inputs:prim", rov_prim_path),  # 设置施力目标
                        ("debug_print.inputs:text", "水下仿真运行中"),  # 调试信息
                        ("debug_print.inputs:logLevel", "Info"),  # 信息级别日志
                        
                        # 设置脚本内容
                        ("quat_to_euler_script.inputs:script", self.get_quat_to_euler_script()),
                        ("buoyancy_script.inputs:script", self.get_buoyancy_script()),
                        ("damping_script.inputs:script", self.get_damping_script()),
                        ("controller_script.inputs:script", self.get_controller_script()),
                        ("thruster_script.inputs:script", self.get_thruster_script()),
                    ],
                    keys.CONNECT: [
                        # 执行流连接
                        ("tick.outputs:tick", "get_transform.inputs:execIn"),  # 触发器到变换获取
                        ("get_transform.outputs:execOut", "debug_print.inputs:execIn"),  # 变换到调试打印
                        
                        # 变换数据流
                        ("get_transform.outputs:rotation", "quat_to_euler_script.inputs:quaternion"),  # 旋转到欧拉角转换
                        ("get_transform.outputs:translation", "extract_z.inputs:vector"),  # 位置到Z分量提取
                        
                        # 物理计算连接
                        ("extract_z.outputs:component", "buoyancy_script.inputs:z_position"),  # Z位置到浮力计算
                        ("volume_const.outputs:value", "buoyancy_script.inputs:volume"),  # 体积到浮力计算
                        ("height_const.outputs:value", "buoyancy_script.inputs:height"),  # 高度到浮力计算
                        ("quat_to_euler_script.outputs:rotation", "buoyancy_script.inputs:rotation"),  # 旋转角度到浮力计算
                        
                        # 阻尼计算连接
                        ("extract_z.outputs:component", "damping_script.inputs:z_position"),  # Z位置到阻尼计算
                        ("max_damping_const.outputs:value", "damping_script.inputs:max_damping"),  # 最大阻尼到阻尼计算
                        ("height_const.outputs:value", "damping_script.inputs:floating_obj_height"),  # 物体高度到阻尼计算
                        
                        # 控制系统连接
                        ("quat_to_euler_script.outputs:rotation", "controller_script.inputs:orientation"),  # 姿态角度到控制器
                        ("gamepad.outputs:leftStickVertical", "thruster_script.inputs:y_stick"),  # 左摇杆Y轴到推进器
                        ("gamepad.outputs:leftStickHorizontal", "thruster_script.inputs:x_stick"),  # 左摇杆X轴到推进器
                        
                        # 力施加连接
                        ("buoyancy_script.outputs:force_vector", "apply_force.inputs:force"),  # 浮力向量到施力节点
                    ],
                },
            )
            
            carb.log_info("水下仿真Action Graph创建成功")
            return True
            
        except Exception as e:
            carb.log_error(f"创建水下仿真图失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_quat_to_euler_script(self):
        """获取四元数转欧拉角脚本"""
        return '''
import math

def setup(db):
    # 初始化函数，在脚本首次运行时调用
    pass

def compute(db):
    # 计算函数，每帧调用
    # 获取四元数输入 [x, y, z, w]
    q = db.inputs.quaternion
    if len(q) != 4:
        db.outputs.rotation = [0.0, 0.0, 0.0]
        return
    
    x, y, z, w = q
    
    # 转换为欧拉角 (roll, pitch, yaw)
    # Roll (绕X轴旋转)
    sinr_cosp = 2 * (w * x + y * z)
    cosr_cosp = 1 - 2 * (x * x + y * y)
    roll = math.atan2(sinr_cosp, cosr_cosp)
    
    # Pitch (绕Y轴旋转)
    sinp = 2 * (w * y - z * x)
    if abs(sinp) >= 1:
        pitch = math.copysign(math.pi / 2, sinp)
    else:
        pitch = math.asin(sinp)
    
    # Yaw (绕Z轴旋转)
    siny_cosp = 2 * (w * z + x * y)
    cosy_cosp = 1 - 2 * (y * y + z * z)
    yaw = math.atan2(siny_cosp, cosy_cosp)
    
    # 转换为度数
    db.outputs.rotation = [
        math.degrees(roll),   # 横滚角
        math.degrees(pitch),  # 俯仰角
        math.degrees(yaw)     # 偏航角
    ]
'''
    
    def get_buoyancy_script(self):
        """获取浮力计算脚本"""
        return '''
import math

def setup(db):
    # 水的物理属性
    db.water_density = 1000.0  # 水密度 kg/m³
    db.gravity = 9.8  # 重力加速度 m/s²

def compute(db):
    # 获取输入参数
    volume = db.inputs.volume        # 物体体积
    height = db.inputs.height        # 物体高度
    z_pos = db.inputs.z_position     # Z轴位置（深度）
    rotation = db.inputs.rotation    # 旋转角度
    
    if z_pos >= 0:
        # 在水面以上 - 无浮力
        db.outputs.x_force = 0.0
        db.outputs.y_force = 0.0
        db.outputs.z_force = 0.0
        db.outputs.force_vector = [0.0, 0.0, 0.0]
        return
    
    # 计算浸没体积
    submerged_height = min(height, abs(z_pos))  # 浸没高度
    submerged_volume = volume * (submerged_height / height)  # 浸没体积
    
    # 基础浮力（向上）
    buoyancy_force = db.water_density * submerged_volume * db.gravity
    
    # 应用旋转效应
    if len(rotation) >= 3:
        roll, pitch, yaw = rotation
        roll_rad = math.radians(roll)    # 转换为弧度
        pitch_rad = math.radians(pitch)
        
        # 根据旋转分配力的方向
        x_force = buoyancy_force * math.sin(pitch_rad) * 0.1  # 俯仰影响X方向力
        y_force = buoyancy_force * math.sin(roll_rad) * 0.1   # 横滚影响Y方向力
        z_force = buoyancy_force * math.cos(roll_rad) * math.cos(pitch_rad)  # 主要向上力
    else:
        x_force = 0.0
        y_force = 0.0
        z_force = buoyancy_force
    
    # 输出各方向的力
    db.outputs.x_force = x_force
    db.outputs.y_force = y_force
    db.outputs.z_force = z_force
    db.outputs.force_vector = [x_force, y_force, z_force]
'''
    
    def get_damping_script(self):
        """获取阻尼计算脚本"""
        return '''
def setup(db):
    # 阻尼计算初始化
    pass

def compute(db):
    # 获取输入参数
    z_pos = db.inputs.z_position              # Z轴位置
    max_damping = db.inputs.max_damping       # 最大阻尼系数
    obj_height = db.inputs.floating_obj_height # 物体高度
    
    if z_pos >= 0:
        # 在水面以上 - 无阻尼
        db.outputs.linear_damping = 0.0
        db.outputs.angular_damping = 0.0
    else:
        # 根据浸没程度计算阻尼
        submerged_ratio = min(1.0, abs(z_pos) / obj_height)  # 浸没比例
        
        # 线性和角度阻尼随浸没程度增加
        db.outputs.linear_damping = max_damping * submerged_ratio        # 线性阻尼
        db.outputs.angular_damping = max_damping * submerged_ratio * 0.5 # 角度阻尼（较小）
'''
    
    def get_controller_script(self):
        """获取PID控制器脚本"""
        return '''
def setup(db):
    # PID控制器参数
    db.kp = 100.0              # 比例增益
    db.ki = 10.0               # 积分增益
    db.kd = 0.01               # 微分增益
    db.integral = 0.0          # 积分累积
    db.previous_error = 0.0    # 上一次误差
    db.saturation_limit = 1000.0  # 输出饱和限制

def compute(db):
    # 获取当前姿态和潜水力
    current_orientation = db.inputs.orientation
    dive_force = getattr(db.inputs, 'dive_force', 0.0)  # 潜水力（可选输入）
    
    if len(current_orientation) >= 3:
        # 简单的横滚角稳定控制
        target_roll = 0.0                    # 目标横滚角（水平）
        current_roll = current_orientation[0] # 当前横滚角
        error = target_roll - current_roll    # 误差
        
        # PID计算
        db.integral += error                              # 积分项累积
        derivative = error - db.previous_error            # 微分项
        
        # PID输出
        output = (db.kp * error +           # 比例项
                 db.ki * db.integral +      # 积分项
                 db.kd * derivative)        # 微分项
        
        # 应用饱和限制
        if output > db.saturation_limit:
            output = db.saturation_limit
        elif output < -db.saturation_limit:
            output = -db.saturation_limit
        
        db.previous_error = error  # 保存当前误差
        
        # 输出控制力
        db.outputs.force = [output, 0.0, dive_force]        # 正向控制力
        db.outputs.minus_force = [-output, 0.0, -dive_force] # 反向控制力
    else:
        # 无有效姿态数据时输出零力
        db.outputs.force = [0.0, 0.0, 0.0]
        db.outputs.minus_force = [0.0, 0.0, 0.0]
'''
    
    def get_thruster_script(self):
        """获取推进器控制脚本"""
        return '''
def setup(db):
    # 推进器参数
    db.max_thrust = 500.0  # 最大推力

def compute(db):
    # 获取摇杆输入
    y_stick = db.inputs.y_stick  # Y轴摇杆（前进/后退）
    x_stick = db.inputs.x_stick  # X轴摇杆（左转/右转）
    
    # 计算各推进器力
    # 前进/后退运动
    forward_thrust = y_stick * db.max_thrust
    
    # 左转/右转运动
    turn_thrust = x_stick * db.max_thrust * 0.5
    
    # 分配到四个推进器
    # 左前推进器：前进力 + 转向力
    db.outputs.left_front = [0.0, forward_thrust + turn_thrust, 0.0]
    # 右前推进器：前进力 - 转向力
    db.outputs.right_front = [0.0, forward_thrust - turn_thrust, 0.0]
    # 左后推进器：前进力 + 转向力
    db.outputs.left_back = [0.0, forward_thrust + turn_thrust, 0.0]
    # 右后推进器：前进力 - 转向力
    db.outputs.right_back = [0.0, forward_thrust - turn_thrust, 0.0]
'''


def main():
    """主函数 - 创建水下Action Graph"""
    builder = UnderwaterActionGraphBuilder()
    
    print("=" * 60)
    print("Isaac Sim 4.5 水下Action Graph构建器")
    print("=" * 60)
    
    # 首先测试基础功能
    print("\n1. 测试基础Action Graph创建...")
    if builder.create_basic_test_graph():
        print("✓ 基础测试图创建成功")
    else:
        print("✗ 基础测试失败")
        return
    
    # 创建完整水下仿真
    print("\n2. 创建水下仿真图...")
    if builder.create_underwater_simulation_graph("/World/ROV"):
        print("✓ 水下仿真图创建成功")
    else:
        print("✗ 水下仿真创建失败")
        return
    
    print("\n" + "=" * 60)
    print("成功: Action Graph已创建!")
    print("=" * 60)
    print("后续步骤:")
    print("1. 打开 Window → Visual Scripting → Action Graph")
    print("2. 选择 /UnderwaterActionGraph")
    print("3. 在 /World/ROV 位置创建ROV模型")
    print("4. 点击播放按钮开始仿真")
    print("5. 连接游戏手柄进行控制")


if __name__ == "__main__":
    main()
