"""
阻尼力计算模块
根据物体在水中的位置计算线性和角度阻尼系数
用于模拟水下物体受到的流体阻尼效应
"""

def setup(db):
    """
    初始化函数
    设置必要的变量或数据结构

    参数:
        db: 数据库对象，用于存储和传递数据

    注意:
        此模块不需要特殊的初始化操作
    """
    pass  # 此情况下不需要初始化操作

def compute(db):
    """
    根据物体位置计算阻尼系数

    阻尼系数随物体浸没深度变化：
    - 完全在水面上方：最小阻尼（空气阻尼）
    - 完全在水面下方：最大阻尼（水阻尼）
    - 部分浸没：按浸没比例线性插值

    参数:
        db: 数据库对象，包含输入和输出数据
            输入参数:
                z_position: 物体在Z轴的位置 (m)
                max_damping: 最大阻尼系数（完全浸没时）
                floating_obj_height: 浮体的高度 (m)
            输出参数:
                linear_damping: 线性阻尼系数
                angular_damping: 角度阻尼系数

    返回:
        无直接返回值，结果存储在 db.outputs 中
    """
    # 获取输入参数
    z_pos = db.inputs.z_position  # 物体Z轴位置 (m)
    max_damp = db.inputs.max_damping  # 最大阻尼系数
    height = db.inputs.floating_obj_height  # 浮体高度 (m)
    half_height = height / 2  # 浮体半高

    # 计算浸没百分比
    # 公式：(-z_pos / height + 0.5)
    # 当z_pos = -height/2时（完全浸没），百分比 = 1.0
    # 当z_pos = height/2时（完全在水面上），百分比 = 0.0
    displacement_percentage = -z_pos / height + 0.5

    damping = 0  # 初始化阻尼值

    # 根据位置计算阻尼系数
    if z_pos >= half_height:
        # 物体完全在水面上方：使用最小阻尼（空气阻尼）
        damping = 0.01
    elif z_pos < -half_height:
        # 物体完全在水面下方：使用最大阻尼（水阻尼）
        damping = max_damp
    elif -half_height <= z_pos < half_height:
        # 物体部分浸没：按浸没比例计算阻尼
        # 浸没越深，阻尼越大
        damping = max_damp * displacement_percentage

    # 输出阻尼系数
    # 线性阻尼和角度阻尼使用相同的值
    db.outputs.linear_damping = damping   # 线性运动阻尼系数
    db.outputs.angular_damping = damping  # 角度运动阻尼系数
    