"""
Isaac Sim 4.5 Working Test Script
Simplified test using correct API patterns
"""

import omni.graph.core as og
import omni.kit.commands
import carb


def test_basic_graph_creation():
    """Test basic graph creation without ActionGraph node type"""
    graph_path = "/TestGraph"
    
    # Delete existing graph
    try:
        omni.kit.commands.execute('DeletePrims', paths=[graph_path])
        carb.log_info("Deleted existing graph")
    except:
        pass
    
    try:
        # Create graph directly
        graph = og.Controller.create_graph({"graph_path": graph_path, "evaluator_name": "execution"})
        
        if graph:
            carb.log_info(f"Successfully created graph: {graph_path}")
            
            # Create nodes using the graph
            keys = og.Controller.Keys
            (_, nodes_created, _, _) = og.Controller.edit(
                graph,
                {
                    keys.CREATE_NODES: [
                        ("OnTick", "omni.graph.action.OnTick"),
                        ("ConstantDouble", "omni.graph.nodes.ConstantDouble"),
                        ("Print", "omni.graph.nodes.Print"),
                    ],
                },
            )
            
            if len(nodes_created) == 3:
                carb.log_info("Successfully created nodes")
                
                # Get node references
                on_tick = nodes_created[0]
                constant = nodes_created[1]
                print_node = nodes_created[2]
                
                # Set constant value
                og.Controller.edit(
                    graph,
                    {
                        keys.SET_VALUES: [
                            (constant.get_attribute("inputs:value"), 42.0),
                        ],
                    },
                )
                
                # Connect nodes
                og.Controller.edit(
                    graph,
                    {
                        keys.CONNECT: [
                            (on_tick.get_attribute("outputs:tick"), 
                             print_node.get_attribute("inputs:execIn")),
                            (constant.get_attribute("outputs:value"), 
                             print_node.get_attribute("inputs:value")),
                        ],
                    },
                )
                
                carb.log_info("Successfully connected nodes")
                return True
            else:
                carb.log_error(f"Expected 3 nodes, got {len(nodes_created)}")
                return False
        else:
            carb.log_error("Failed to create graph")
            return False
            
    except Exception as e:
        carb.log_error(f"Error in test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_script_node():
    """Test script node creation"""
    graph_path = "/ScriptTestGraph"
    
    # Delete existing graph
    try:
        omni.kit.commands.execute('DeletePrims', paths=[graph_path])
    except:
        pass
    
    try:
        # Create graph
        graph = og.Controller.create_graph({"graph_path": graph_path, "evaluator_name": "execution"})
        
        if graph:
            keys = og.Controller.Keys
            (_, nodes_created, _, _) = og.Controller.edit(
                graph,
                {
                    keys.CREATE_NODES: [
                        ("OnTick", "omni.graph.action.OnTick"),
                        ("ScriptNode", "omni.graph.scriptnode.ScriptNode"),
                    ],
                },
            )
            
            if len(nodes_created) == 2:
                script_node = nodes_created[1]
                
                # Set script content
                script_content = '''
def setup(db):
    db.counter = 0
    print("Script node setup complete")

def compute(db):
    db.counter += 1
    if db.counter <= 3:
        print(f"Script execution #{db.counter}")
    db.outputs.result = db.counter * 10.0
'''
                
                og.Controller.edit(
                    graph,
                    {
                        keys.SET_VALUES: [
                            (script_node.get_attribute("inputs:script"), script_content),
                        ],
                    },
                )
                
                carb.log_info("Script node created successfully")
                return True
            else:
                carb.log_error("Failed to create script nodes")
                return False
        else:
            carb.log_error("Failed to create script graph")
            return False
            
    except Exception as e:
        carb.log_error(f"Error creating script node: {e}")
        import traceback
        traceback.print_exc()
        return False


def list_available_node_types():
    """List available node types for debugging"""
    try:
        node_types = og.get_node_types()
        carb.log_info(f"Found {len(node_types)} node types")
        
        # Filter for common types
        action_types = [t for t in node_types if 'action' in t.lower()]
        graph_types = [t for t in node_types if 'graph' in t.lower()]
        script_types = [t for t in node_types if 'script' in t.lower()]
        
        print("Action types:", action_types[:5])  # Show first 5
        print("Graph types:", graph_types[:5])
        print("Script types:", script_types[:5])
        
        return True
    except Exception as e:
        carb.log_error(f"Error listing node types: {e}")
        return False


def main():
    """Main test function"""
    print("=" * 60)
    print("Isaac Sim 4.5 Working Test")
    print("=" * 60)
    
    # List available node types first
    print("\n1. Listing available node types...")
    if list_available_node_types():
        print("✓ Node types listed successfully")
    else:
        print("✗ Failed to list node types")
    
    # Test basic graph creation
    print("\n2. Testing basic graph creation...")
    if test_basic_graph_creation():
        print("✓ Basic graph creation test passed")
    else:
        print("✗ Basic graph creation test failed")
        return
    
    # Test script node
    print("\n3. Testing script node creation...")
    if test_script_node():
        print("✓ Script node test passed")
    else:
        print("✗ Script node test failed")
        return
    
    print("\n" + "=" * 60)
    print("All tests completed!")
    print("=" * 60)
    print("Next steps:")
    print("1. Open Window → Visual Scripting → Action Graph")
    print("2. Select /TestGraph or /ScriptTestGraph")
    print("3. Click Play button to see execution")
    print("4. Check console for script output")


if __name__ == "__main__":
    main()
