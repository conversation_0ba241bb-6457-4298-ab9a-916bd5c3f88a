"""
Isaac Sim 4.5 水下仿真 Action Graph Python API 实现
使用纯Python代码创建和配置完整的水下机器人仿真Action Graph
"""

import omni.graph.core as og
import omni.kit.commands
from pxr import Gf, Sdf
import carb


class UnderwaterActionGraphBuilder:
    """水下仿真Action Graph构建器"""
    
    def __init__(self, graph_path="/ActionGraph"):
        """
        初始化Action Graph构建器
        
        Args:
            graph_path: Action Graph在场景中的路径
        """
        self.graph_path = graph_path
        self.graph = None
        self.nodes = {}
        
    def create_graph(self):
        """创建Action Graph"""
        try:
            # 删除已存在的图
            omni.kit.commands.execute('DeletePrims', paths=[self.graph_path])
        except:
            pass
            
        # 创建新的Action Graph
        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.action.ActionGraph',
            node_name='ActionGraph'
        )
        
        # 获取图对象
        self.graph = og.get_graph_by_path(self.graph_path)
        carb.log_info(f"创建Action Graph: {self.graph_path}")
        
    def add_script_node(self, node_name, script_path, inputs_config, outputs_config):
        """
        添加Python脚本节点
        
        Args:
            node_name: 节点名称
            script_path: 脚本文件路径
            inputs_config: 输入端口配置 [(name, type, default_value), ...]
            outputs_config: 输出端口配置 [(name, type), ...]
        """
        # 创建Python脚本节点
        node_path = f"{self.graph_path}/{node_name}"
        
        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.scriptnode.ScriptNode',
            node_name=node_name
        )
        
        node = og.get_node_by_path(node_path)
        self.nodes[node_name] = node
        
        # 读取脚本内容
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                script_content = f.read()
        except FileNotFoundError:
            carb.log_error(f"脚本文件未找到: {script_path}")
            return None
            
        # 设置脚本内容
        script_attr = node.get_attribute("inputs:script")
        script_attr.set(script_content)
        
        # 配置输入端口
        for input_name, input_type, default_value in inputs_config:
            self._add_input_port(node, input_name, input_type, default_value)
            
        # 配置输出端口
        for output_name, output_type in outputs_config:
            self._add_output_port(node, output_name, output_type)
            
        carb.log_info(f"添加脚本节点: {node_name}")
        return node
        
    def _add_input_port(self, node, name, port_type, default_value=None):
        """添加输入端口"""
        try:
            input_attr = node.create_attribute(f"inputs:{name}", port_type)
            if default_value is not None:
                input_attr.set(default_value)
        except Exception as e:
            carb.log_error(f"创建输入端口失败 {name}: {e}")
            
    def _add_output_port(self, node, name, port_type):
        """添加输出端口"""
        try:
            node.create_attribute(f"outputs:{name}", port_type)
        except Exception as e:
            carb.log_error(f"创建输出端口失败 {name}: {e}")
    
    def add_constant_node(self, node_name, value_type, value):
        """添加常量节点"""
        node_path = f"{self.graph_path}/{node_name}"
        
        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.nodes.ConstantToken' if value_type == og.Type(og.BaseDataType.TOKEN) else 'omni.graph.nodes.ConstantDouble',
            node_name=node_name
        )
        
        node = og.get_node_by_path(node_path)
        self.nodes[node_name] = node
        
        # 设置常量值
        value_attr = node.get_attribute("inputs:value")
        value_attr.set(value)
        
        return node
    
    def add_transform_node(self, node_name, prim_path):
        """添加Transform获取节点"""
        node_path = f"{self.graph_path}/{node_name}"
        
        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.nodes.GetPrimLocalToWorldTransform',
            node_name=node_name
        )
        
        node = og.get_node_by_path(node_path)
        self.nodes[node_name] = node
        
        # 设置目标Prim路径
        prim_attr = node.get_attribute("inputs:prim")
        prim_attr.set(prim_path)
        
        return node
    
    def add_gamepad_node(self, node_name, gamepad_index=0):
        """添加游戏手柄输入节点"""
        node_path = f"{self.graph_path}/{node_name}"
        
        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.nodes.GamepadInput',
            node_name=node_name
        )
        
        node = og.get_node_by_path(node_path)
        self.nodes[node_name] = node
        
        # 设置手柄索引
        index_attr = node.get_attribute("inputs:gamepadIndex")
        index_attr.set(gamepad_index)
        
        return node
    
    def add_apply_force_node(self, node_name, prim_path):
        """添加施加力节点"""
        node_path = f"{self.graph_path}/{node_name}"

        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.nodes.ApplyForce',
            node_name=node_name
        )

        node = og.get_node_by_path(node_path)
        self.nodes[node_name] = node

        # 设置目标Prim路径
        prim_attr = node.get_attribute("inputs:prim")
        prim_attr.set(prim_path)

        return node

    def add_vector_math_node(self, node_name, operation="add"):
        """添加向量数学运算节点"""
        node_path = f"{self.graph_path}/{node_name}"

        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.nodes.VectorMath',
            node_name=node_name
        )

        node = og.get_node_by_path(node_path)
        self.nodes[node_name] = node

        # 设置运算类型
        op_attr = node.get_attribute("inputs:operation")
        op_attr.set(operation)

        return node

    def add_make_array_node(self, node_name, array_size=3):
        """添加数组构建节点"""
        node_path = f"{self.graph_path}/{node_name}"

        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.nodes.MakeArray',
            node_name=node_name
        )

        node = og.get_node_by_path(node_path)
        self.nodes[node_name] = node

        # 设置数组大小
        size_attr = node.get_attribute("inputs:arraySize")
        size_attr.set(array_size)

        return node

    def add_extract_component_node(self, node_name, component_index=2):
        """添加向量分量提取节点"""
        node_path = f"{self.graph_path}/{node_name}"

        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.nodes.ExtractComponent',
            node_name=node_name
        )

        node = og.get_node_by_path(node_path)
        self.nodes[node_name] = node

        # 设置提取的分量索引
        index_attr = node.get_attribute("inputs:index")
        index_attr.set(component_index)

        return node
    
    def connect_nodes(self, source_node_name, source_attr, target_node_name, target_attr):
        """连接节点"""
        try:
            source_node = self.nodes[source_node_name]
            target_node = self.nodes[target_node_name]
            
            source_attribute = source_node.get_attribute(source_attr)
            target_attribute = target_node.get_attribute(target_attr)
            
            og.Controller.connect(source_attribute, target_attribute)
            carb.log_info(f"连接: {source_node_name}.{source_attr} -> {target_node_name}.{target_attr}")
            
        except Exception as e:
            carb.log_error(f"连接失败: {e}")
    
    def build_underwater_simulation_graph(self, rov_prim_path="/World/ROV"):
        """构建完整的水下仿真Action Graph"""
        carb.log_info("开始构建水下仿真Action Graph...")
        
        # 1. 创建基础图
        self.create_graph()
        
        # 2. 添加Transform获取节点
        self.add_transform_node("GetROVTransform", rov_prim_path)
        
        # 3. 添加游戏手柄输入
        self.add_gamepad_node("GamepadInput")
        
        # 4. 添加常量节点
        self.add_constant_node("VolumeConstant", og.Type(og.BaseDataType.DOUBLE), 1.0)
        self.add_constant_node("HeightConstant", og.Type(og.BaseDataType.DOUBLE), 2.0)
        self.add_constant_node("MaxDampingConstant", og.Type(og.BaseDataType.DOUBLE), 10.0)
        
        # 5. 添加脚本节点
        self._add_all_script_nodes()
        
        # 6. 添加数学运算节点
        self.add_extract_component_node("ExtractZPosition", 2)  # 提取Z分量
        self.add_make_array_node("MakeBuoyancyVector", 3)  # 构建浮力向量
        self.add_vector_math_node("AddForces", "add")  # 力向量相加

        # 7. 添加力应用节点
        self.add_apply_force_node("ApplyTotalForce", rov_prim_path)

        # 8. 建立连接
        self._create_connections()

        carb.log_info("水下仿真Action Graph构建完成！")
    
    def _add_all_script_nodes(self):
        """添加所有脚本节点"""
        # 四元数转欧拉角节点
        self.add_script_node(
            "QuatToEuler",
            "scripts/quat_to_euler.py",
            [("quaternion", og.Type(og.BaseDataType.DOUBLE, 4), [0.0, 0.0, 0.0, 1.0])],
            [("rotation", og.Type(og.BaseDataType.DOUBLE, 3))]
        )
        
        # 浮力计算节点
        self.add_script_node(
            "BuoyancyForces",
            "scripts/buoyancy_forces.py",
            [
                ("volume", og.Type(og.BaseDataType.DOUBLE), 1.0),
                ("height", og.Type(og.BaseDataType.DOUBLE), 2.0),
                ("z_position", og.Type(og.BaseDataType.DOUBLE), 0.0),
                ("rotation", og.Type(og.BaseDataType.DOUBLE, 3), [0.0, 0.0, 0.0])
            ],
            [
                ("x_force", og.Type(og.BaseDataType.DOUBLE)),
                ("y_force", og.Type(og.BaseDataType.DOUBLE)),
                ("z_force", og.Type(og.BaseDataType.DOUBLE))
            ]
        )
        
        # 阻尼计算节点
        self.add_script_node(
            "Damping",
            "scripts/damping.py",
            [
                ("z_position", og.Type(og.BaseDataType.DOUBLE), 0.0),
                ("max_damping", og.Type(og.BaseDataType.DOUBLE), 10.0),
                ("floating_obj_height", og.Type(og.BaseDataType.DOUBLE), 2.0)
            ],
            [
                ("linear_damping", og.Type(og.BaseDataType.DOUBLE)),
                ("angular_damping", og.Type(og.BaseDataType.DOUBLE))
            ]
        )
        
        # PID控制器节点
        self.add_script_node(
            "PIDController",
            "scripts/controller.py",
            [
                ("orientation", og.Type(og.BaseDataType.DOUBLE), 0.0),
                ("dive_force", og.Type(og.BaseDataType.DOUBLE), 0.0)
            ],
            [
                ("force", og.Type(og.BaseDataType.DOUBLE, 3)),
                ("minus_force", og.Type(og.BaseDataType.DOUBLE, 3))
            ]
        )
        
        # 推进器控制节点
        self.add_script_node(
            "ThrusterControl",
            "scripts/linear_angular_control.py",
            [
                ("x_stick", og.Type(og.BaseDataType.DOUBLE), 0.0),
                ("y_stick", og.Type(og.BaseDataType.DOUBLE), 0.0)
            ],
            [
                ("left_front", og.Type(og.BaseDataType.DOUBLE, 3)),
                ("right_front", og.Type(og.BaseDataType.DOUBLE, 3)),
                ("left_back", og.Type(og.BaseDataType.DOUBLE, 3)),
                ("right_back", og.Type(og.BaseDataType.DOUBLE, 3))
            ]
        )
    
    def _create_connections(self):
        """创建所有节点连接"""
        # Transform数据连接
        self.connect_nodes("GetROVTransform", "outputs:rotation", "QuatToEuler", "inputs:quaternion")
        self.connect_nodes("GetROVTransform", "outputs:translation", "ExtractZPosition", "inputs:vector")

        # Z位置提取和分发
        self.connect_nodes("ExtractZPosition", "outputs:component", "BuoyancyForces", "inputs:z_position")
        self.connect_nodes("ExtractZPosition", "outputs:component", "Damping", "inputs:z_position")

        # 常量连接
        self.connect_nodes("VolumeConstant", "outputs:value", "BuoyancyForces", "inputs:volume")
        self.connect_nodes("HeightConstant", "outputs:value", "BuoyancyForces", "inputs:height")
        self.connect_nodes("HeightConstant", "outputs:value", "Damping", "inputs:floating_obj_height")
        self.connect_nodes("MaxDampingConstant", "outputs:value", "Damping", "inputs:max_damping")

        # 脚本节点间连接
        self.connect_nodes("QuatToEuler", "outputs:rotation", "BuoyancyForces", "inputs:rotation")

        # 游戏手柄连接
        self.connect_nodes("GamepadInput", "outputs:leftStickX", "ThrusterControl", "inputs:x_stick")
        self.connect_nodes("GamepadInput", "outputs:leftStickY", "ThrusterControl", "inputs:y_stick")

        # 浮力向量构建
        self.connect_nodes("BuoyancyForces", "outputs:x_force", "MakeBuoyancyVector", "inputs:a")
        self.connect_nodes("BuoyancyForces", "outputs:y_force", "MakeBuoyancyVector", "inputs:b")
        self.connect_nodes("BuoyancyForces", "outputs:z_force", "MakeBuoyancyVector", "inputs:c")

        # PID控制连接（使用roll角作为示例）
        # 注意：这里需要从欧拉角中提取roll分量
        # self.connect_nodes("QuatToEuler", "outputs:rotation[0]", "PIDController", "inputs:orientation")

        # 最终力合成和应用
        self.connect_nodes("MakeBuoyancyVector", "outputs:array", "ApplyTotalForce", "inputs:force")

        carb.log_info("所有节点连接完成")


def main():
    """主函数 - 创建水下仿真Action Graph"""
    builder = UnderwaterActionGraphBuilder("/ActionGraph")
    
    # 构建完整的仿真图
    builder.build_underwater_simulation_graph("/World/ROV")
    
    print("水下仿真Action Graph创建完成！")
    print("请在Isaac Sim中检查Action Graph编辑器查看结果。")


if __name__ == "__main__":
    main()
