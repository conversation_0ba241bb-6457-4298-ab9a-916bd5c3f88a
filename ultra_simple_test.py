"""
Ultra Simple Isaac Sim 4.5 Test
Most basic approach to test OmniGraph API
"""

import omni.graph.core as og
import omni.kit.commands
import carb


def test_node_types_first():
    """First check what node types are actually available"""
    try:
        node_types = og.get_node_types()
        print(f"Total node types available: {len(node_types)}")
        
        # Look for specific patterns
        action_nodes = [t for t in node_types if 'action' in t.lower()]
        print(f"Action nodes: {action_nodes}")
        
        graph_nodes = [t for t in node_types if 'graph' in t.lower()]
        print(f"Graph nodes: {graph_nodes}")
        
        script_nodes = [t for t in node_types if 'script' in t.lower()]
        print(f"Script nodes: {script_nodes}")
        
        constant_nodes = [t for t in node_types if 'constant' in t.lower()]
        print(f"Constant nodes: {constant_nodes}")
        
        print_nodes = [t for t in node_types if 'print' in t.lower()]
        print(f"Print nodes: {print_nodes}")
        
        return True
    except Exception as e:
        print(f"Error getting node types: {e}")
        return False


def test_basic_controller_edit():
    """Test the most basic Controller.edit functionality"""
    graph_path = "/UltraSimpleGraph"
    
    # Delete existing
    try:
        omni.kit.commands.execute('DeletePrims', paths=[graph_path])
        print("Deleted existing graph")
    except:
        pass
    
    try:
        # Try the most basic Controller.edit approach
        keys = og.Controller.Keys
        result = og.Controller.edit(
            {"graph_path": graph_path, "evaluator_name": "execution"},
            {}  # Empty edit - just create the graph
        )
        
        print(f"Controller.edit result: {result}")
        
        if result and len(result) >= 1:
            graph = result[0]
            print(f"Graph created: {graph}")
            return True
        else:
            print("Failed to create graph")
            return False
            
    except Exception as e:
        print(f"Error in basic controller edit: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_create_single_node():
    """Test creating just one node"""
    graph_path = "/SingleNodeGraph"
    
    # Delete existing
    try:
        omni.kit.commands.execute('DeletePrims', paths=[graph_path])
    except:
        pass
    
    try:
        # First create empty graph
        keys = og.Controller.Keys
        result = og.Controller.edit(
            {"graph_path": graph_path, "evaluator_name": "execution"},
            {}
        )
        
        if result and len(result) >= 1:
            graph = result[0]
            print(f"Graph created: {graph}")
            
            # Now try to add a single node - use a very basic type
            result2 = og.Controller.edit(
                graph,
                {
                    keys.CREATE_NODES: [
                        ("TestNode", "omni.graph.nodes.ConstantBool"),  # Try boolean constant
                    ],
                },
            )
            
            print(f"Node creation result: {result2}")
            
            if result2 and len(result2) >= 2:
                nodes_created = result2[1]
                print(f"Nodes created: {len(nodes_created)}")
                return True
            else:
                print("No nodes were created")
                return False
        else:
            print("Failed to create graph")
            return False
            
    except Exception as e:
        print(f"Error creating single node: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Ultra simple main function"""
    print("=" * 50)
    print("Ultra Simple Isaac Sim 4.5 Test")
    print("=" * 50)
    
    print("\n1. Checking available node types...")
    if test_node_types_first():
        print("✓ Node types check completed")
    else:
        print("✗ Node types check failed")
        return
    
    print("\n2. Testing basic Controller.edit...")
    if test_basic_controller_edit():
        print("✓ Basic Controller.edit works")
    else:
        print("✗ Basic Controller.edit failed")
        return
    
    print("\n3. Testing single node creation...")
    if test_create_single_node():
        print("✓ Single node creation works")
    else:
        print("✗ Single node creation failed")
        return
    
    print("\n" + "=" * 50)
    print("All basic tests passed!")
    print("Now we know the API basics work.")
    print("=" * 50)


if __name__ == "__main__":
    main()
