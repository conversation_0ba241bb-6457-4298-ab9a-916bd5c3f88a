# Isaac Sim Action Graph 配置指南

## 概述
本指南详细说明如何在Isaac Sim的Action Graph中配置和连接Python脚本节点，实现完整的水下仿真系统。

## Action Graph节点配置

### 1. 基础节点设置

#### 创建Python Script节点
1. 在Action Graph编辑器中右键 → **Add Node**
2. 搜索 "Python Script" → 选择 **omni.graph.scriptnode.ScriptNode**
3. 为每个脚本创建独立的节点

#### 脚本加载方式
```python
# 方法1: 直接在节点中粘贴脚本代码
# 方法2: 使用文件路径引用
script_path = "scripts/quat_to_euler.py"
```

### 2. 节点详细配置

#### A. 四元数转换节点 (quat_to_euler.py)
**输入端口配置:**
- `quaternion` (float[4]): 四元数数组 [x,y,z,w]

**输出端口配置:**
- `rotation` (float[3]): 欧拉角数组 [roll,pitch,yaw]

**连接示例:**
```
Prim Transform → Get Prim Rotation → quat_to_euler → rotation
```

#### B. 浮力计算节点 (buoyancy_forces.py)
**输入端口配置:**
- `volume` (float): 物体体积
- `height` (float): 物体高度
- `z_position` (float): Z轴位置
- `rotation` (float[3]): 旋转角度

**输出端口配置:**
- `x_force` (float): X方向浮力
- `y_force` (float): Y方向浮力
- `z_force` (float): Z方向浮力

**连接示例:**
```
Prim Transform → Get Prim Translation → [2] → z_position
quat_to_euler → rotation → buoyancy_forces → rotation
Constant (1.0) → volume
Constant (2.0) → height
```

#### C. 阻尼计算节点 (damping.py)
**输入端口配置:**
- `z_position` (float): Z轴位置
- `max_damping` (float): 最大阻尼系数
- `floating_obj_height` (float): 物体高度

**输出端口配置:**
- `linear_damping` (float): 线性阻尼
- `angular_damping` (float): 角度阻尼

#### D. PID控制节点 (controller.py)
**输入端口配置:**
- `orientation` (float): 当前姿态角度
- `dive_force` (float): 潜水力

**输出端口配置:**
- `force` (float[3]): 控制力向量
- `minus_force` (float[3]): 反向控制力向量

#### E. 推进器控制节点 (linear_angular_control.py)
**输入端口配置:**
- `x_stick` (float): X轴操纵杆值
- `y_stick` (float): Y轴操纵杆值

**输出端口配置:**
- `left_front` (float[3]): 左前推进器力
- `right_front` (float[3]): 右前推进器力
- `left_back` (float[3]): 左后推进器力
- `right_back` (float[3]): 右后推进器力

### 3. 完整连接流程

#### 输入数据获取
```
场景对象 → Get Prim Transform → 位置/旋转数据
游戏手柄 → Gamepad Input → 操纵杆数据
时间 → Simulation Time → 时间步长
```

#### 数据处理链
```
四元数 → quat_to_euler → 欧拉角
                      ↓
位置 + 欧拉角 → buoyancy_forces → 浮力向量
                              ↓
位置 → damping → 阻尼系数 → Set Rigid Body Properties
                              ↓
操纵杆 → linear_angular_control → 推进器力
                              ↓
姿态角 → controller → 稳定力 → 力合成 → Apply Forces
```

#### 输出应用
```
浮力向量 → Vector Add → 总力向量 → Apply Forces → 物体
阻尼系数 → Set Rigid Body Properties → 物体刚体属性
```

### 4. 节点参数配置

#### 常量节点设置
创建Constant节点为脚本提供固定参数：

```
物体体积: 1.0 m³
物体高度: 2.0 m
最大阻尼: 10.0
水密度: 1000.0 kg/m³
重力加速度: 9.8 m/s²
```

#### 游戏手柄配置
1. 添加 **Gamepad Input** 节点
2. 配置手柄ID和按键映射
3. 连接摇杆轴到相应的脚本输入

```
Left Stick X → x_stick
Left Stick Y → y_stick
R2 Trigger → dive_force (通过条件节点转换)
```

### 5. 调试和监控

#### 添加调试节点
```
Print → 输出关键数值到控制台
Graph Variable → 存储中间计算结果
Curve → 绘制数值变化曲线
```

#### 性能监控
```
Profiler → 监控节点执行时间
Frame Rate → 检查仿真帧率
Memory Usage → 监控内存使用
```

### 6. 高级配置

#### 条件执行
使用Branch节点根据条件执行不同的计算路径：
```
水面检测 → Branch → 浮力计算 / 空气阻力计算
```

#### 数据缓存
使用Delay节点缓存历史数据：
```
当前角度 → Delay → 前一帧角度 → PID微分项计算
```

#### 多物体支持
为每个物体创建独立的脚本节点实例：
```
ROV_1 → 脚本组1 → 力输出1
ROV_2 → 脚本组2 → 力输出2
```

### 7. 常见问题解决

#### 数据类型不匹配
- 确保输入输出端口类型正确
- 使用类型转换节点进行数据格式转换

#### 执行顺序问题
- 使用Execution Pin确保正确的执行顺序
- 避免循环依赖

#### 性能优化
- 合并相似的计算节点
- 使用条件执行避免不必要的计算
- 适当降低更新频率

### 8. 测试验证

#### 单元测试
为每个脚本节点创建独立的测试图：
```
测试输入 → 脚本节点 → 预期输出验证
```

#### 集成测试
创建完整的仿真场景进行端到端测试：
```
完整场景 → 所有节点 → 物理行为验证
```

#### 参数调优
使用滑块节点实时调整参数：
```
UI Slider → 参数输入 → 实时效果观察
```

## 总结

通过以上配置，您可以在Isaac Sim中构建一个完整的水下仿真系统。关键要点：

1. **模块化设计**: 每个脚本作为独立节点，便于调试和维护
2. **数据流清晰**: 明确的输入输出连接，避免数据混乱
3. **参数可调**: 使用常量和滑块节点便于参数调整
4. **性能优化**: 合理的执行顺序和条件判断
5. **调试友好**: 添加监控和调试节点便于问题排查

按照这个指南配置后，您就可以在Isaac Sim中运行完整的水下机器人仿真了。
