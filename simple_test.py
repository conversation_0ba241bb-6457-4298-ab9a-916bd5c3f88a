"""
Isaac Sim 4.5 简化测试脚本
用于验证Action Graph Python API的基本功能
"""

import omni.graph.core as og
import omni.kit.commands
import carb


def create_simple_action_graph():
    """创建一个简单的Action Graph进行测试"""
    graph_path = "/ActionGraph"
    
    try:
        # 删除已存在的图
        omni.kit.commands.execute('DeletePrims', paths=[graph_path])
        carb.log_info("删除已存在的Action Graph")
    except:
        pass
    
    try:
        # 创建新的Action Graph
        omni.kit.commands.execute('CreateNode',
            graph_path=graph_path,
            node_type='omni.graph.action.ActionGraph',
            node_name='ActionGraph'
        )
        carb.log_info(f"成功创建Action Graph: {graph_path}")
        
        # 获取图对象
        graph = og.get_graph_by_path(graph_path)
        if graph:
            carb.log_info("成功获取图对象")
        else:
            carb.log_error("无法获取图对象")
            return False
            
        # 创建一个简单的常量节点
        constant_node_path = f"{graph_path}/TestConstant"
        omni.kit.commands.execute('CreateNode',
            graph_path=graph_path,
            node_type='omni.graph.nodes.ConstantDouble',
            node_name='TestConstant'
        )
        carb.log_info("成功创建常量节点")
        
        # 获取节点并设置值
        constant_node = og.get_node_by_path(constant_node_path)
        if constant_node:
            value_attr = constant_node.get_attribute("inputs:value")
            value_attr.set(42.0)
            carb.log_info("成功设置常量值: 42.0")
        else:
            carb.log_error("无法获取常量节点")
            return False
        
        # 创建一个Print节点用于输出
        print_node_path = f"{graph_path}/TestPrint"
        omni.kit.commands.execute('CreateNode',
            graph_path=graph_path,
            node_type='omni.graph.nodes.Print',
            node_name='TestPrint'
        )
        carb.log_info("成功创建Print节点")
        
        # 连接节点
        print_node = og.get_node_by_path(print_node_path)
        if print_node:
            # 连接常量到Print节点
            constant_output = constant_node.get_attribute("outputs:value")
            print_input = print_node.get_attribute("inputs:value")
            
            og.Controller.connect(constant_output, print_input)
            carb.log_info("成功连接节点")
        else:
            carb.log_error("无法获取Print节点")
            return False
            
        carb.log_info("简单Action Graph创建完成！")
        return True
        
    except Exception as e:
        carb.log_error(f"创建Action Graph时出错: {e}")
        return False


def test_script_node():
    """测试Python脚本节点"""
    graph_path = "/ActionGraph"
    
    try:
        # 创建Python脚本节点
        script_node_path = f"{graph_path}/TestScript"
        omni.kit.commands.execute('CreateNode',
            graph_path=graph_path,
            node_type='omni.graph.scriptnode.ScriptNode',
            node_name='TestScript'
        )
        
        # 获取脚本节点
        script_node = og.get_node_by_path(script_node_path)
        if not script_node:
            carb.log_error("无法获取脚本节点")
            return False
        
        # 设置简单的脚本内容
        script_content = '''
def setup(db):
    """初始化脚本"""
    db.counter = 0
    print("脚本节点初始化完成")

def compute(db):
    """每帧执行的计算"""
    db.counter += 1
    db.outputs.result = db.counter * 2.0
    if db.counter <= 5:  # 只打印前5次
        print(f"脚本执行第 {db.counter} 次，结果: {db.outputs.result}")
'''
        
        # 设置脚本内容
        script_attr = script_node.get_attribute("inputs:script")
        script_attr.set(script_content)
        
        # 创建输出端口
        script_node.create_attribute("outputs:result", og.Type(og.BaseDataType.DOUBLE))
        
        carb.log_info("Python脚本节点创建成功")
        return True
        
    except Exception as e:
        carb.log_error(f"创建脚本节点时出错: {e}")
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("Isaac Sim 4.5 Action Graph Python API 测试")
    print("=" * 50)
    
    # 测试基本的Action Graph创建
    if create_simple_action_graph():
        print("✓ 基本Action Graph创建成功")
    else:
        print("✗ 基本Action Graph创建失败")
        return
    
    # 测试Python脚本节点
    if test_script_node():
        print("✓ Python脚本节点创建成功")
    else:
        print("✗ Python脚本节点创建失败")
        return
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)
    print("请检查以下内容:")
    print("1. 打开 Window → Visual Scripting → Action Graph")
    print("2. 在左侧面板选择 /ActionGraph")
    print("3. 查看创建的节点和连接")
    print("4. 点击播放按钮观察脚本执行")
    print("5. 查看控制台输出")


if __name__ == "__main__":
    main()
