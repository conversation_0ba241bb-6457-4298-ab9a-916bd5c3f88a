"""
Isaac Sim 4.5 Underwater Simulation Action Graph Builder
Pure Python implementation using OmniGraph Controller API
"""

import omni.graph.core as og
import omni.kit.commands
import carb


class UnderwaterActionGraphBuilder:
    """Underwater simulation Action Graph builder using Isaac Sim 4.5 API"""
    
    def __init__(self, graph_path="/ActionGraph"):
        """
        Initialize Action Graph builder
        
        Args:
            graph_path: Path for the Action Graph in the scene
        """
        self.graph_path = graph_path
        self.graph = None
        self.nodes = {}
        
    def create_graph(self):
        """Create the Action Graph"""
        try:
            # Delete existing graph
            omni.kit.commands.execute('DeletePrims', paths=[self.graph_path])
        except:
            pass
            
        # Create new Action Graph using Controller API
        keys = og.Controller.Keys
        try:
            (self.graph, _, _, _) = og.Controller.edit(
                {"graph_path": self.graph_path, "evaluator_name": "execution"},
                {
                    keys.CREATE_NODES: [
                        ("ActionGraph", "omni.graph.action.ActionGraph"),
                    ],
                },
            )
            carb.log_info(f"Created Action Graph: {self.graph_path}")
            return True
        except Exception as e:
            carb.log_error(f"Failed to create Action Graph: {e}")
            return False
        
    def add_nodes(self, node_definitions):
        """
        Add multiple nodes to the graph
        
        Args:
            node_definitions: List of (node_name, node_type) tuples
        """
        if not self.graph:
            carb.log_error("Graph not created yet")
            return False
            
        keys = og.Controller.Keys
        try:
            (_, nodes_created, _, _) = og.Controller.edit(
                self.graph,
                {
                    keys.CREATE_NODES: node_definitions,
                },
            )
            
            # Store node references
            for i, (node_name, _) in enumerate(node_definitions):
                if i < len(nodes_created):
                    self.nodes[node_name] = nodes_created[i]
                    carb.log_info(f"Created node: {node_name}")
            
            return True
        except Exception as e:
            carb.log_error(f"Failed to create nodes: {e}")
            return False
    
    def set_node_attributes(self, node_name, attributes):
        """
        Set attributes for a node
        
        Args:
            node_name: Name of the node
            attributes: Dictionary of attribute_name: value pairs
        """
        if node_name not in self.nodes:
            carb.log_error(f"Node {node_name} not found")
            return False
            
        node = self.nodes[node_name]
        keys = og.Controller.Keys
        
        try:
            og.Controller.edit(
                self.graph,
                {
                    keys.SET_VALUES: [
                        (node.get_attribute(f"inputs:{attr_name}"), value)
                        for attr_name, value in attributes.items()
                    ],
                },
            )
            carb.log_info(f"Set attributes for {node_name}")
            return True
        except Exception as e:
            carb.log_error(f"Failed to set attributes for {node_name}: {e}")
            return False
    
    def connect_nodes(self, connections):
        """
        Connect nodes
        
        Args:
            connections: List of (source_node, source_attr, target_node, target_attr) tuples
        """
        if not self.graph:
            carb.log_error("Graph not created yet")
            return False
            
        keys = og.Controller.Keys
        connection_list = []
        
        for source_node, source_attr, target_node, target_attr in connections:
            if source_node not in self.nodes or target_node not in self.nodes:
                carb.log_error(f"Node not found: {source_node} or {target_node}")
                continue
                
            source = self.nodes[source_node].get_attribute(f"outputs:{source_attr}")
            target = self.nodes[target_node].get_attribute(f"inputs:{target_attr}")
            connection_list.append((source, target))
        
        try:
            og.Controller.edit(
                self.graph,
                {
                    keys.CONNECT: connection_list,
                },
            )
            carb.log_info(f"Connected {len(connection_list)} node pairs")
            return True
        except Exception as e:
            carb.log_error(f"Failed to connect nodes: {e}")
            return False
    
    def build_underwater_simulation_graph(self, rov_prim_path="/World/ROV"):
        """Build complete underwater simulation Action Graph"""
        carb.log_info("Building underwater simulation Action Graph...")
        
        # 1. Create the graph
        if not self.create_graph():
            return False
        
        # 2. Define all nodes
        node_definitions = [
            # Input nodes
            ("OnTick", "omni.graph.action.OnTick"),
            ("GetROVTransform", "omni.graph.nodes.GetPrimLocalToWorldTransform"),
            ("GamepadInput", "omni.graph.nodes.GamepadInput"),
            
            # Constant nodes
            ("VolumeConstant", "omni.graph.nodes.ConstantDouble"),
            ("HeightConstant", "omni.graph.nodes.ConstantDouble"),
            ("MaxDampingConstant", "omni.graph.nodes.ConstantDouble"),
            
            # Math nodes
            ("ExtractZPosition", "omni.graph.nodes.ExtractComponent"),
            ("MakeBuoyancyVector", "omni.graph.nodes.MakeArray"),
            
            # Script nodes (we'll set these up separately)
            ("QuatToEulerScript", "omni.graph.scriptnode.ScriptNode"),
            ("BuoyancyScript", "omni.graph.scriptnode.ScriptNode"),
            ("DampingScript", "omni.graph.scriptnode.ScriptNode"),
            ("ThrusterScript", "omni.graph.scriptnode.ScriptNode"),
            
            # Output nodes
            ("ApplyBuoyancyForce", "omni.graph.nodes.ApplyForce"),
        ]
        
        # 3. Create all nodes
        if not self.add_nodes(node_definitions):
            return False
        
        # 4. Configure node attributes
        self._configure_node_attributes(rov_prim_path)
        
        # 5. Set up script nodes
        self._setup_script_nodes()
        
        # 6. Connect nodes
        self._create_connections()
        
        carb.log_info("Underwater simulation Action Graph completed!")
        return True
    
    def _configure_node_attributes(self, rov_prim_path):
        """Configure attributes for all nodes"""
        # Configure transform node
        self.set_node_attributes("GetROVTransform", {"prim": rov_prim_path})
        
        # Configure constants
        self.set_node_attributes("VolumeConstant", {"value": 8.0})  # 2x2x2 cube volume
        self.set_node_attributes("HeightConstant", {"value": 2.0})
        self.set_node_attributes("MaxDampingConstant", {"value": 10.0})
        
        # Configure extract component (Z position)
        self.set_node_attributes("ExtractZPosition", {"index": 2})
        
        # Configure make array
        self.set_node_attributes("MakeBuoyancyVector", {"arraySize": 3})
        
        # Configure apply force
        self.set_node_attributes("ApplyBuoyancyForce", {"prim": rov_prim_path})
        
        # Configure gamepad
        self.set_node_attributes("GamepadInput", {"gamepadIndex": 0})
    
    def _setup_script_nodes(self):
        """Setup Python script nodes with code"""
        
        # Quaternion to Euler script
        quat_to_euler_script = '''
import math

def setup(db):
    pass

def compute(db):
    # Get quaternion input [x, y, z, w]
    q = db.inputs.quaternion
    if len(q) != 4:
        db.outputs.rotation = [0.0, 0.0, 0.0]
        return
    
    x, y, z, w = q
    
    # Convert to Euler angles (roll, pitch, yaw)
    # Roll (x-axis rotation)
    sinr_cosp = 2 * (w * x + y * z)
    cosr_cosp = 1 - 2 * (x * x + y * y)
    roll = math.atan2(sinr_cosp, cosr_cosp)
    
    # Pitch (y-axis rotation)
    sinp = 2 * (w * y - z * x)
    if abs(sinp) >= 1:
        pitch = math.copysign(math.pi / 2, sinp)
    else:
        pitch = math.asin(sinp)
    
    # Yaw (z-axis rotation)
    siny_cosp = 2 * (w * z + x * y)
    cosy_cosp = 1 - 2 * (y * y + z * z)
    yaw = math.atan2(siny_cosp, cosy_cosp)
    
    # Convert to degrees
    db.outputs.rotation = [
        math.degrees(roll),
        math.degrees(pitch),
        math.degrees(yaw)
    ]
'''
        
        # Buoyancy calculation script
        buoyancy_script = '''
import math

def setup(db):
    db.water_density = 1000.0  # kg/m³
    db.gravity = 9.8  # m/s²

def compute(db):
    volume = db.inputs.volume
    height = db.inputs.height
    z_pos = db.inputs.z_position
    rotation = db.inputs.rotation
    
    if z_pos >= 0:
        # Above water
        db.outputs.x_force = 0.0
        db.outputs.y_force = 0.0
        db.outputs.z_force = 0.0
        return
    
    # Calculate submerged volume
    submerged_height = min(height, abs(z_pos))
    submerged_volume = volume * (submerged_height / height)
    
    # Basic buoyancy force (upward)
    buoyancy_force = db.water_density * submerged_volume * db.gravity
    
    # Apply rotation effects (simplified)
    if len(rotation) >= 3:
        roll, pitch, yaw = rotation
        roll_rad = math.radians(roll)
        pitch_rad = math.radians(pitch)
        
        # Distribute force based on rotation
        db.outputs.x_force = buoyancy_force * math.sin(pitch_rad) * 0.1
        db.outputs.y_force = buoyancy_force * math.sin(roll_rad) * 0.1
        db.outputs.z_force = buoyancy_force * math.cos(roll_rad) * math.cos(pitch_rad)
    else:
        db.outputs.x_force = 0.0
        db.outputs.y_force = 0.0
        db.outputs.z_force = buoyancy_force
'''
        
        # Set script contents
        script_configs = {
            "QuatToEulerScript": quat_to_euler_script,
            "BuoyancyScript": buoyancy_script,
        }
        
        for node_name, script_content in script_configs.items():
            self.set_node_attributes(node_name, {"script": script_content})
    
    def _create_connections(self):
        """Create all node connections"""
        connections = [
            # Execution flow
            ("OnTick", "tick", "GetROVTransform", "execIn"),
            
            # Transform data flow
            ("GetROVTransform", "rotation", "QuatToEulerScript", "quaternion"),
            ("GetROVTransform", "translation", "ExtractZPosition", "vector"),
            
            # Constants to scripts
            ("VolumeConstant", "value", "BuoyancyScript", "volume"),
            ("HeightConstant", "value", "BuoyancyScript", "height"),
            
            # Position and rotation to buoyancy
            ("ExtractZPosition", "component", "BuoyancyScript", "z_position"),
            ("QuatToEulerScript", "rotation", "BuoyancyScript", "rotation"),
            
            # Buoyancy forces to vector
            ("BuoyancyScript", "x_force", "MakeBuoyancyVector", "a"),
            ("BuoyancyScript", "y_force", "MakeBuoyancyVector", "b"),
            ("BuoyancyScript", "z_force", "MakeBuoyancyVector", "c"),
            
            # Apply forces
            ("MakeBuoyancyVector", "array", "ApplyBuoyancyForce", "force"),
        ]
        
        self.connect_nodes(connections)


def main():
    """Main function to create underwater simulation Action Graph"""
    builder = UnderwaterActionGraphBuilder("/ActionGraph")
    
    if builder.build_underwater_simulation_graph("/World/ROV"):
        print("Underwater simulation Action Graph created successfully!")
        print("Check the Action Graph editor to see the nodes and connections.")
    else:
        print("Failed to create underwater simulation Action Graph.")


if __name__ == "__main__":
    main()
