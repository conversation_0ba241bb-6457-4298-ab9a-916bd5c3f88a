"""
四元数到欧拉角转换模块
提供将四元数表示的旋转转换为欧拉角的功能
用于水下机器人姿态表示的转换
"""

import numpy as np

def setup(db):
    """
    初始化函数
    设置必要的变量或数据结构

    参数:
        db: 数据库对象，用于存储和传递数据

    注意:
        此模块不需要特殊的初始化操作
    """
    pass  # 此情况下不需要初始化操作

def compute(db):
    """
    将四元数转换为欧拉角

    四元数是表示三维旋转的数学工具，具有无奇点、计算稳定等优点
    欧拉角是更直观的旋转表示方法，包含横滚角、俯仰角和偏航角

    转换使用标准的四元数到欧拉角转换公式，避免万向锁问题

    参数:
        db: 数据库对象，包含输入和输出数据
            输入参数:
                quaternion: 四元数 [x, y, z, w]
                           其中 w 是标量部分，x,y,z 是向量部分
            输出参数:
                rotation: 欧拉角 [roll, pitch, yaw] (度)
                         roll: 横滚角，绕X轴旋转
                         pitch: 俯仰角，绕Y轴旋转
                         yaw: 偏航角，绕Z轴旋转

    返回:
        无直接返回值，结果存储在 db.outputs.rotation 中
    """
    # 获取输入四元数
    quaternion = db.inputs.quaternion

    # 提取四元数分量
    x = quaternion[0]  # 四元数X分量
    y = quaternion[1]  # 四元数Y分量
    z = quaternion[2]  # 四元数Z分量
    w = quaternion[3]  # 四元数W分量（标量部分）

    # 计算横滚角 (Roll) - 绕X轴旋转
    # 使用 atan2 函数避免除零问题和象限歧义
    t0 = 2 * (w * x + y * z)  # 分子项
    t1 = 1 - 2 * (x * x + y * y)  # 分母项
    roll = np.arctan2(t0, t1)

    # 计算俯仰角 (Pitch) - 绕Y轴旋转
    # 使用 arcsin 函数，需要限制输入范围避免数值错误
    t2 = 2 * (w * y - z * x)
    t2 = np.clip(t2, -1.0, 1.0)  # 限制在[-1,1]范围内，避免arcsin域错误
    pitch = np.arcsin(t2)

    # 计算偏航角 (Yaw) - 绕Z轴旋转
    # 同样使用 atan2 函数避免除零和象限问题
    t3 = 2 * (w * z + x * y)  # 分子项
    t4 = 1 - 2 * (y * y + z * z)  # 分母项
    yaw = np.arctan2(t3, t4)

    # 将弧度转换为度数，便于理解和使用
    roll = np.degrees(roll)   # 横滚角（度）
    pitch = np.degrees(pitch) # 俯仰角（度）
    yaw = np.degrees(yaw)     # 偏航角（度）

    # 输出欧拉角数组
    db.outputs.rotation = [roll, pitch, yaw]
