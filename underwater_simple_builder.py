"""
Simplified Underwater Action Graph Builder for Isaac Sim 4.5
Focus on core functionality with verified node types
"""

import omni.graph.core as og
import omni.kit.commands
import carb


class SimpleUnderwaterBuilder:
    """Build a simplified underwater simulation Action Graph"""
    
    def __init__(self, graph_path="/SimpleUnderwaterGraph"):
        self.graph_path = graph_path
        self.graph_handle = None
        
    def create_basic_underwater_graph(self, rov_prim_path="/World/ROV"):
        """Create a basic underwater simulation with verified node types"""
        carb.log_info("Creating simplified underwater Action Graph...")
        
        # Delete existing graph
        try:
            omni.kit.commands.execute('DeletePrims', paths=[self.graph_path])
        except:
            pass
        
        try:
            keys = og.Controller.Keys
            (self.graph_handle, nodes_created, _, _) = og.Controller.edit(
                {"graph_path": self.graph_path, "evaluator_name": "execution"},
                {
                    keys.CREATE_NODES: [
                        # Execution trigger
                        ("tick", "omni.graph.action.OnTick"),
                        
                        # Input nodes - using verified types
                        ("get_transform", "omni.graph.nodes.GetPrimLocalToWorldTransform"),
                        
                        # Constants for ROV properties
                        ("volume_const", "omni.graph.nodes.ConstantDouble"),
                        ("height_const", "omni.graph.nodes.ConstantDouble"),
                        ("water_level_const", "omni.graph.nodes.ConstantDouble"),
                        
                        # Math nodes
                        ("extract_position", "omni.graph.nodes.ExtractComponent"),
                        ("extract_rotation", "omni.graph.nodes.ExtractComponent"),
                        
                        # Script nodes for physics
                        ("buoyancy_script", "omni.graph.scriptnode.ScriptNode"),
                        ("underwater_physics", "omni.graph.scriptnode.ScriptNode"),
                        
                        # Output nodes
                        ("apply_force", "omni.graph.nodes.ApplyForce"),
                        ("debug_print", "omni.graph.ui_nodes.PrintText"),
                    ],
                    keys.SET_VALUES: [
                        # Configure input nodes
                        ("get_transform.inputs:prim", rov_prim_path),
                        
                        # Set ROV physical properties
                        ("volume_const.inputs:value", 8.0),  # ROV volume m³
                        ("height_const.inputs:value", 2.0),  # ROV height m
                        ("water_level_const.inputs:value", 0.0),  # Water surface at Z=0
                        
                        # Configure math nodes
                        ("extract_position.inputs:index", 2),  # Extract Z position
                        ("extract_rotation.inputs:index", 3),  # Extract W component of quaternion
                        
                        # Configure output nodes
                        ("apply_force.inputs:prim", rov_prim_path),
                        ("debug_print.inputs:text", "Underwater Physics Active"),
                        ("debug_print.inputs:logLevel", "Info"),
                        
                        # Set script contents
                        ("buoyancy_script.inputs:script", self.get_buoyancy_script()),
                        ("underwater_physics.inputs:script", self.get_physics_script()),
                    ],
                    keys.CONNECT: [
                        # Execution flow
                        ("tick.outputs:tick", "get_transform.inputs:execIn"),
                        ("get_transform.outputs:execOut", "buoyancy_script.inputs:execIn"),
                        ("buoyancy_script.outputs:execOut", "underwater_physics.inputs:execIn"),
                        ("underwater_physics.outputs:execOut", "apply_force.inputs:execIn"),
                        ("apply_force.outputs:execOut", "debug_print.inputs:execIn"),
                        
                        # Data flow for buoyancy calculation
                        ("get_transform.outputs:translation", "extract_position.inputs:vector"),
                        ("extract_position.outputs:component", "buoyancy_script.inputs:z_position"),
                        ("volume_const.outputs:value", "buoyancy_script.inputs:volume"),
                        ("height_const.outputs:value", "buoyancy_script.inputs:height"),
                        ("water_level_const.outputs:value", "buoyancy_script.inputs:water_level"),
                        
                        # Physics processing
                        ("buoyancy_script.outputs:buoyancy_force", "underwater_physics.inputs:buoyancy"),
                        ("get_transform.outputs:rotation", "underwater_physics.inputs:rotation"),
                        
                        # Force application
                        ("underwater_physics.outputs:total_force", "apply_force.inputs:force"),
                    ],
                },
            )
            
            carb.log_info("Simplified underwater Action Graph created successfully")
            return True
            
        except Exception as e:
            carb.log_error(f"Failed to create underwater graph: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_buoyancy_script(self):
        """Simple buoyancy calculation script"""
        return '''
def setup(db):
    db.water_density = 1000.0  # kg/m³
    db.gravity = 9.8  # m/s²

def compute(db):
    # Get inputs
    z_pos = db.inputs.z_position
    volume = db.inputs.volume
    height = db.inputs.height
    water_level = db.inputs.water_level
    
    # Calculate depth below water surface
    depth = water_level - z_pos
    
    if depth <= 0:
        # Above water - no buoyancy
        db.outputs.buoyancy_force = [0.0, 0.0, 0.0]
        db.outputs.is_submerged = False
    else:
        # Calculate submerged volume
        submerged_ratio = min(1.0, depth / height)
        submerged_volume = volume * submerged_ratio
        
        # Calculate buoyancy force (upward)
        buoyancy_magnitude = db.water_density * submerged_volume * db.gravity
        
        # Output as 3D vector (upward force)
        db.outputs.buoyancy_force = [0.0, 0.0, buoyancy_magnitude]
        db.outputs.is_submerged = True
        db.outputs.submersion_ratio = submerged_ratio
'''
    
    def get_physics_script(self):
        """Combined physics processing script"""
        return '''
import math

def setup(db):
    db.max_damping = 10.0
    db.drag_coefficient = 0.5

def compute(db):
    # Get inputs
    buoyancy = db.inputs.buoyancy
    rotation = db.inputs.rotation
    
    # Start with buoyancy force
    total_force = list(buoyancy) if buoyancy else [0.0, 0.0, 0.0]
    
    # Add simple damping if submerged
    if len(buoyancy) >= 3 and buoyancy[2] > 0:
        # Apply water resistance (opposing motion)
        damping_force = [0.0, 0.0, -50.0]  # Simple downward damping
        
        # Add damping to total force
        for i in range(3):
            total_force[i] += damping_force[i]
    
    # Apply rotation effects if quaternion is available
    if len(rotation) >= 4:
        x, y, z, w = rotation
        
        # Simple roll correction - add small stabilizing force
        roll_correction = x * -100.0  # Oppose roll rotation
        total_force[0] += roll_correction
    
    # Output the combined force
    db.outputs.total_force = total_force
    
    # Debug output
    force_magnitude = math.sqrt(sum(f*f for f in total_force))
    db.outputs.force_magnitude = force_magnitude
'''


def main():
    """Main function to create simplified underwater Action Graph"""
    builder = SimpleUnderwaterBuilder()
    
    print("=" * 60)
    print("Simplified Underwater Action Graph Builder")
    print("=" * 60)
    
    print("\nCreating simplified underwater simulation...")
    if builder.create_basic_underwater_graph("/World/ROV"):
        print("✓ Simplified underwater graph created successfully")
        
        print("\n" + "=" * 60)
        print("SUCCESS: Simplified Action Graph created!")
        print("=" * 60)
        print("Features included:")
        print("- ✓ Buoyancy calculation based on submersion")
        print("- ✓ Water resistance/damping")
        print("- ✓ Basic roll stabilization")
        print("- ✓ Real-time force application")
        print("\nNext steps:")
        print("1. Open Window → Visual Scripting → Action Graph")
        print("2. Select /SimpleUnderwaterGraph")
        print("3. Create ROV primitive at /World/ROV")
        print("4. Add RigidBody component to ROV")
        print("5. Click Play to start simulation")
        print("\nTo add gamepad control:")
        print("- Manually add gamepad nodes in Action Graph editor")
        print("- Connect to thruster control scripts")
        
        return True
    else:
        print("✗ Failed to create simplified underwater graph")
        return False


if __name__ == "__main__":
    main()
