"""
Simplified Underwater Action Graph Builder for Isaac Sim 4.5
Focus on core functionality with verified node types
"""

import omni.graph.core as og
import omni.kit.commands
import carb


class SimpleUnderwaterBuilder:
    """Build a simplified underwater simulation Action Graph"""
    
    def __init__(self, graph_path="/SimpleUnderwaterGraph"):
        self.graph_path = graph_path
        self.graph_handle = None
        
    def create_basic_underwater_graph(self, rov_prim_path="/World/ROV"):
        """Create a basic underwater simulation with verified node types"""
        carb.log_info("Creating simplified underwater Action Graph...")
        
        # Delete existing graph
        try:
            omni.kit.commands.execute('DeletePrims', paths=[self.graph_path])
        except:
            pass
        
        try:
            keys = og.Controller.Keys
            (self.graph_handle, nodes_created, _, _) = og.Controller.edit(
                {"graph_path": self.graph_path, "evaluator_name": "execution"},
                {
                    keys.CREATE_NODES: [
                        # Execution trigger
                        ("tick", "omni.graph.action.OnTick"),

                        # Input nodes - using only verified types
                        ("get_transform", "omni.graph.nodes.GetPrimLocalToWorldTransform"),

                        # Constants for ROV properties
                        ("volume_const", "omni.graph.nodes.ConstantDouble"),
                        ("height_const", "omni.graph.nodes.ConstantDouble"),
                        ("water_level_const", "omni.graph.nodes.ConstantDouble"),

                        # Script nodes for physics (do all math in scripts)
                        ("underwater_physics", "omni.graph.scriptnode.ScriptNode"),

                        # Output nodes
                        ("apply_force", "omni.graph.nodes.ApplyForce"),
                        ("debug_print", "omni.graph.ui_nodes.PrintText"),
                    ],
                    keys.SET_VALUES: [
                        # Configure input nodes
                        ("get_transform.inputs:prim", rov_prim_path),

                        # Set ROV physical properties
                        ("volume_const.inputs:value", 8.0),  # ROV volume m³
                        ("height_const.inputs:value", 2.0),  # ROV height m
                        ("water_level_const.inputs:value", 0.0),  # Water surface at Z=0

                        # Configure output nodes
                        ("apply_force.inputs:prim", rov_prim_path),
                        ("debug_print.inputs:text", "Underwater Physics Active"),
                        ("debug_print.inputs:logLevel", "Info"),

                        # Set script contents
                        ("underwater_physics.inputs:script", self.get_complete_physics_script()),
                    ],
                    keys.CONNECT: [
                        # Execution flow
                        ("tick.outputs:tick", "get_transform.inputs:execIn"),
                        ("get_transform.outputs:execOut", "underwater_physics.inputs:execIn"),
                        ("underwater_physics.outputs:execOut", "apply_force.inputs:execIn"),
                        ("apply_force.outputs:execOut", "debug_print.inputs:execIn"),

                        # Data flow - all processing in one script
                        ("get_transform.outputs:translation", "underwater_physics.inputs:position"),
                        ("get_transform.outputs:rotation", "underwater_physics.inputs:rotation"),
                        ("volume_const.outputs:value", "underwater_physics.inputs:volume"),
                        ("height_const.outputs:value", "underwater_physics.inputs:height"),
                        ("water_level_const.outputs:value", "underwater_physics.inputs:water_level"),

                        # Force application
                        ("underwater_physics.outputs:total_force", "apply_force.inputs:force"),
                    ],
                },
            )
            
            carb.log_info("Simplified underwater Action Graph created successfully")
            return True
            
        except Exception as e:
            carb.log_error(f"Failed to create underwater graph: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_complete_physics_script(self):
        """Complete underwater physics in one script"""
        return '''
import math

def setup(db):
    db.water_density = 1000.0  # kg/m³
    db.gravity = 9.8  # m/s²
    db.max_damping = 10.0
    db.drag_coefficient = 0.5

def compute(db):
    # Get inputs
    position = db.inputs.position  # 3D position vector
    rotation = db.inputs.rotation  # Quaternion [x,y,z,w]
    volume = db.inputs.volume      # Object volume
    height = db.inputs.height      # Object height
    water_level = db.inputs.water_level  # Water surface level

    # Initialize total force
    total_force = [0.0, 0.0, 0.0]

    # Extract Z position (depth)
    if len(position) >= 3:
        z_pos = position[2]

        # Calculate depth below water surface
        depth = water_level - z_pos

        if depth > 0:
            # Object is submerged - calculate buoyancy
            submerged_ratio = min(1.0, depth / height)
            submerged_volume = volume * submerged_ratio

            # Calculate buoyancy force (upward)
            buoyancy_magnitude = db.water_density * submerged_volume * db.gravity
            total_force[2] += buoyancy_magnitude  # Add upward force

            # Add water resistance (damping)
            damping_force = buoyancy_magnitude * 0.1  # 10% of buoyancy as damping
            total_force[2] -= damping_force  # Subtract damping

            # Apply rotation-based stabilization
            if len(rotation) >= 4:
                x, y, z, w = rotation

                # Simple roll and pitch correction
                roll_correction = x * -200.0   # Oppose roll rotation
                pitch_correction = y * -200.0  # Oppose pitch rotation

                total_force[0] += roll_correction
                total_force[1] += pitch_correction

    # Output the combined force
    db.outputs.total_force = total_force

    # Debug outputs
    force_magnitude = math.sqrt(sum(f*f for f in total_force))
    db.outputs.force_magnitude = force_magnitude
    db.outputs.is_submerged = depth > 0 if len(position) >= 3 else False
'''


def main():
    """Main function to create simplified underwater Action Graph"""
    builder = SimpleUnderwaterBuilder()
    
    print("=" * 60)
    print("Simplified Underwater Action Graph Builder")
    print("=" * 60)
    
    print("\nCreating simplified underwater simulation...")
    if builder.create_basic_underwater_graph("/World/ROV"):
        print("✓ Simplified underwater graph created successfully")
        
        print("\n" + "=" * 60)
        print("SUCCESS: Simplified Action Graph created!")
        print("=" * 60)
        print("Features included:")
        print("- ✓ Buoyancy calculation based on submersion")
        print("- ✓ Water resistance/damping")
        print("- ✓ Basic roll stabilization")
        print("- ✓ Real-time force application")
        print("\nNext steps:")
        print("1. Open Window → Visual Scripting → Action Graph")
        print("2. Select /SimpleUnderwaterGraph")
        print("3. Create ROV primitive at /World/ROV")
        print("4. Add RigidBody component to ROV")
        print("5. Click Play to start simulation")
        print("\nTo add gamepad control:")
        print("- Manually add gamepad nodes in Action Graph editor")
        print("- Connect to thruster control scripts")
        
        return True
    else:
        print("✗ Failed to create simplified underwater graph")
        return False


if __name__ == "__main__":
    main()
