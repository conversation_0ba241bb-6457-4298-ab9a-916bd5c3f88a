# Isaac Sim 4.5 Working API Patterns

## Key Discovery: Use `og.Controller.create_graph()` instead of ActionGraph node

The main issue was trying to create an "ActionGraph" node type, which doesn't exist. Instead, use the Controller to create the graph directly.

## Working Pattern

### ❌ Wrong Approach
```python
# This doesn't work - ActionGraph is not a node type
keys.CREATE_NODES: [
    ("ActionGraph", "omni.graph.action.ActionGraph"),  # ERROR!
]
```

### ✅ Correct Approach
```python
# Step 1: Create graph directly
graph = og.Controller.create_graph({
    "graph_path": "/MyGraph", 
    "evaluator_name": "execution"
})

# Step 2: Add nodes to the graph
keys = og.Controller.Keys
(_, nodes_created, _, _) = og.Controller.edit(
    graph,
    {
        keys.CREATE_NODES: [
            ("OnTick", "omni.graph.action.OnTick"),
            ("MyScript", "omni.graph.scriptnode.ScriptNode"),
        ],
    },
)
```

## Complete Working Example

```python
import omni.graph.core as og
import omni.kit.commands
import carb

def create_working_graph():
    graph_path = "/WorkingGraph"
    
    # Delete existing
    try:
        omni.kit.commands.execute('DeletePrims', paths=[graph_path])
    except:
        pass
    
    # Create graph
    graph = og.Controller.create_graph({
        "graph_path": graph_path, 
        "evaluator_name": "execution"
    })
    
    if graph:
        # Create nodes
        keys = og.Controller.Keys
        (_, nodes, _, _) = og.Controller.edit(
            graph,
            {
                keys.CREATE_NODES: [
                    ("OnTick", "omni.graph.action.OnTick"),
                    ("Constant", "omni.graph.nodes.ConstantDouble"),
                    ("Print", "omni.graph.nodes.Print"),
                ],
            },
        )
        
        # Configure nodes
        og.Controller.edit(
            graph,
            {
                keys.SET_VALUES: [
                    (nodes[1].get_attribute("inputs:value"), 42.0),
                ],
            },
        )
        
        # Connect nodes
        og.Controller.edit(
            graph,
            {
                keys.CONNECT: [
                    (nodes[0].get_attribute("outputs:tick"), 
                     nodes[2].get_attribute("inputs:execIn")),
                    (nodes[1].get_attribute("outputs:value"), 
                     nodes[2].get_attribute("inputs:value")),
                ],
            },
        )
        
        return True
    return False
```

## Common Node Types That Work

### Execution Nodes
- `omni.graph.action.OnTick` - Execution trigger
- `omni.graph.action.OnPlaybackTick` - Playback trigger

### Data Input Nodes
- `omni.graph.nodes.GetPrimLocalToWorldTransform` - Get transform
- `omni.graph.nodes.ConstantDouble` - Constant number
- `omni.graph.nodes.ConstantVector3` - Constant vector

### Math Nodes
- `omni.graph.nodes.ExtractComponent` - Extract vector component
- `omni.graph.nodes.MakeArray` - Create array
- `omni.graph.nodes.Add` - Addition
- `omni.graph.nodes.Multiply` - Multiplication

### Script Nodes
- `omni.graph.scriptnode.ScriptNode` - Python script

### Output Nodes
- `omni.graph.nodes.ApplyForce` - Apply physics force
- `omni.graph.nodes.Print` - Debug output

## Script Node Pattern

```python
script_content = '''
def setup(db):
    # Initialize variables
    db.my_variable = 0
    print("Script initialized")

def compute(db):
    # Process inputs
    input_value = db.inputs.my_input
    
    # Calculate output
    db.outputs.my_output = input_value * 2
    
    # Debug (limit frequency)
    db.my_variable += 1
    if db.my_variable % 60 == 0:  # Every 60 frames
        print(f"Processed: {input_value}")
'''

# Set script content
og.Controller.edit(
    graph,
    {
        keys.SET_VALUES: [
            (script_node.get_attribute("inputs:script"), script_content),
        ],
    },
)
```

## Error Handling Pattern

```python
try:
    graph = og.Controller.create_graph({
        "graph_path": graph_path, 
        "evaluator_name": "execution"
    })
    
    if not graph:
        carb.log_error("Failed to create graph")
        return False
        
    # Continue with node creation...
    
except Exception as e:
    carb.log_error(f"Error: {e}")
    import traceback
    traceback.print_exc()
    return False
```

## Testing Your Setup

### Step 1: Run Basic Test
```python
exec(open('working_test.py').read())
```

### Step 2: Run Minimal Underwater Sim
```python
exec(open('minimal_underwater.py').read())
```

### Step 3: Check Results
1. Open **Window → Visual Scripting → Action Graph**
2. Select your graph from the left panel
3. Click Play to start execution
4. Check console for output

## Key Takeaways

1. **Don't create ActionGraph nodes** - create graphs directly
2. **Use `og.Controller.create_graph()`** for graph creation
3. **Use `og.Controller.edit()`** for all modifications
4. **Handle errors gracefully** with try-catch
5. **Test incrementally** - start simple
6. **Check console output** for debugging

This pattern should work reliably with Isaac Sim 4.5!
