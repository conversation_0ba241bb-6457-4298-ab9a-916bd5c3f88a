"""
Step by <PERSON> Isaac Si<PERSON> 4.5 Test
Build up functionality piece by piece
"""

import omni.graph.core as og
import omni.kit.commands
import carb


class StepByStepTester:
    """Test Isaac Sim 4.5 OmniGraph step by step"""
    
    def __init__(self):
        self.available_nodes = []
        self.working_nodes = []
        
    def step1_discover_nodes(self):
        """Step 1: Discover what node types actually exist"""
        print("Step 1: Discovering available node types...")
        
        try:
            self.available_nodes = og.get_node_types()
            print(f"Found {len(self.available_nodes)} total node types")
            
            # Look for essential types
            essential_patterns = [
                ('action', 'Action/execution nodes'),
                ('constant', 'Constant value nodes'),
                ('print', 'Debug output nodes'),
                ('script', 'Python script nodes'),
                ('math', 'Math operation nodes'),
                ('transform', 'Transform nodes'),
                ('force', 'Physics force nodes')
            ]
            
            for pattern, description in essential_patterns:
                matches = [t for t in self.available_nodes if pattern in t.lower()]
                print(f"{description}: {matches[:3]}...")  # Show first 3
                
            return True
            
        except Exception as e:
            print(f"Error discovering nodes: {e}")
            return False
    
    def step2_create_empty_graph(self):
        """Step 2: Create an empty graph"""
        print("\nStep 2: Creating empty graph...")
        
        graph_path = "/Step2Graph"
        
        try:
            # Delete existing
            omni.kit.commands.execute('DeletePrims', paths=[graph_path])
        except:
            pass
        
        try:
            result = og.Controller.edit(
                {"graph_path": graph_path, "evaluator_name": "execution"},
                {}
            )
            
            if result and len(result) >= 1:
                graph = result[0]
                print(f"✓ Empty graph created: {graph}")
                return graph
            else:
                print("✗ Failed to create empty graph")
                return None
                
        except Exception as e:
            print(f"✗ Error creating empty graph: {e}")
            return None
    
    def step3_test_node_creation(self, graph):
        """Step 3: Test creating individual nodes"""
        print("\nStep 3: Testing individual node creation...")
        
        # Try different node types one by one
        test_nodes = [
            ("ConstantBool", "omni.graph.nodes.ConstantBool"),
            ("ConstantDouble", "omni.graph.nodes.ConstantDouble"),
            ("ConstantFloat", "omni.graph.nodes.ConstantFloat"),
            ("ConstantInt", "omni.graph.nodes.ConstantInt"),
        ]
        
        keys = og.Controller.Keys
        
        for node_name, node_type in test_nodes:
            try:
                result = og.Controller.edit(
                    graph,
                    {
                        keys.CREATE_NODES: [
                            (node_name, node_type),
                        ],
                    },
                )
                
                if result and len(result) >= 2 and len(result[1]) > 0:
                    print(f"✓ Created {node_name} ({node_type})")
                    self.working_nodes.append((node_name, node_type))
                else:
                    print(f"✗ Failed to create {node_name} ({node_type})")
                    
            except Exception as e:
                print(f"✗ Error creating {node_name}: {e}")
        
        return len(self.working_nodes) > 0
    
    def step4_test_attributes(self, graph):
        """Step 4: Test setting node attributes"""
        print("\nStep 4: Testing attribute setting...")
        
        if not self.working_nodes:
            print("No working nodes to test attributes")
            return False
        
        keys = og.Controller.Keys
        success_count = 0
        
        # Test setting attributes on working nodes
        for node_name, node_type in self.working_nodes:
            try:
                # Get the node
                node_path = f"/Step2Graph/{node_name}"
                node = og.get_node_by_path(node_path)
                
                if node:
                    # Try to set a value based on node type
                    if "Bool" in node_type:
                        test_value = True
                        attr_name = "inputs:value"
                    elif "Double" in node_type or "Float" in node_type:
                        test_value = 42.0
                        attr_name = "inputs:value"
                    elif "Int" in node_type:
                        test_value = 42
                        attr_name = "inputs:value"
                    else:
                        continue
                    
                    # Try to set the attribute
                    attribute = node.get_attribute(attr_name)
                    if attribute:
                        og.Controller.edit(
                            graph,
                            {
                                keys.SET_VALUES: [
                                    (attribute, test_value),
                                ],
                            },
                        )
                        print(f"✓ Set {node_name}.{attr_name} = {test_value}")
                        success_count += 1
                    else:
                        print(f"✗ Attribute {attr_name} not found on {node_name}")
                else:
                    print(f"✗ Node {node_name} not found")
                    
            except Exception as e:
                print(f"✗ Error setting attribute on {node_name}: {e}")
        
        return success_count > 0
    
    def step5_test_script_node(self, graph):
        """Step 5: Test Python script node"""
        print("\nStep 5: Testing Python script node...")
        
        # Find script node types
        script_types = [t for t in self.available_nodes if 'script' in t.lower()]
        
        if not script_types:
            print("No script node types found")
            return False
        
        print(f"Available script types: {script_types}")
        
        keys = og.Controller.Keys
        
        for script_type in script_types[:2]:  # Try first 2 types
            try:
                result = og.Controller.edit(
                    graph,
                    {
                        keys.CREATE_NODES: [
                            ("TestScript", script_type),
                        ],
                    },
                )
                
                if result and len(result) >= 2 and len(result[1]) > 0:
                    print(f"✓ Created script node with type: {script_type}")
                    
                    # Try to set script content
                    script_node = result[1][0]
                    script_content = '''
def setup(db):
    print("Script node setup called")

def compute(db):
    print("Script node compute called")
'''
                    
                    try:
                        script_attr = script_node.get_attribute("inputs:script")
                        if script_attr:
                            og.Controller.edit(
                                graph,
                                {
                                    keys.SET_VALUES: [
                                        (script_attr, script_content),
                                    ],
                                },
                            )
                            print("✓ Script content set successfully")
                            return True
                        else:
                            print("✗ Script attribute not found")
                    except Exception as e:
                        print(f"✗ Error setting script content: {e}")
                else:
                    print(f"✗ Failed to create script node: {script_type}")
                    
            except Exception as e:
                print(f"✗ Error creating script node {script_type}: {e}")
        
        return False
    
    def run_all_steps(self):
        """Run all test steps"""
        print("=" * 60)
        print("Isaac Sim 4.5 Step-by-Step API Test")
        print("=" * 60)
        
        # Step 1: Discover nodes
        if not self.step1_discover_nodes():
            print("FAILED at Step 1")
            return False
        
        # Step 2: Create empty graph
        graph = self.step2_create_empty_graph()
        if not graph:
            print("FAILED at Step 2")
            return False
        
        # Step 3: Test node creation
        if not self.step3_test_node_creation(graph):
            print("FAILED at Step 3")
            return False
        
        # Step 4: Test attributes
        if not self.step4_test_attributes(graph):
            print("FAILED at Step 4")
            return False
        
        # Step 5: Test script node
        if not self.step5_test_script_node(graph):
            print("WARNING: Script node test failed, but continuing...")
        
        print("\n" + "=" * 60)
        print("SUCCESS: Basic API functionality confirmed!")
        print("=" * 60)
        print(f"Working node types: {len(self.working_nodes)}")
        for name, type_name in self.working_nodes:
            print(f"  - {name}: {type_name}")
        
        return True


def main():
    """Main function"""
    tester = StepByStepTester()
    success = tester.run_all_steps()
    
    if success:
        print("\nNext steps:")
        print("1. Open Window → Visual Scripting → Action Graph")
        print("2. Select /Step2Graph to see created nodes")
        print("3. Now we can build more complex graphs!")
    else:
        print("\nSome tests failed. Check the error messages above.")


if __name__ == "__main__":
    main()
