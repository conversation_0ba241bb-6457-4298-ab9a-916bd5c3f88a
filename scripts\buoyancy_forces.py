"""
旋转浮力模块
提供计算物体浮力并根据物体姿态进行旋转变换的功能
用于水下机器人或浮体的三维浮力仿真，考虑物体的旋转姿态
"""

import math
import numpy as np

def setup(db):
    """
    初始化函数
    设置必要的变量或数据结构

    参数:
        db: 数据库对象，用于存储和传递数据

    注意:
        此模块不需要特殊的初始化操作
    """
    pass  # 此情况下不需要初始化操作

def compute(db):
    """
    计算浮力并根据物体姿态进行旋转变换

    该函数不仅计算浮力大小，还考虑物体的旋转姿态，
    将垂直向上的浮力向量转换到物体的局部坐标系中

    参数:
        db: 数据库对象，包含输入和输出数据
            输入参数:
                - volume: 物体总体积 (m³)
                - height: 物体高度 (m)
                - z_position: 物体在Z轴的位置 (m)
                - rotation: 物体的旋转角度 [roll, pitch, yaw] (度)
            输出参数:
                - x_force: X方向的浮力分量 (N)
                - y_force: Y方向的浮力分量 (N)
                - z_force: Z方向的浮力分量 (N)

    返回:
        无直接返回值，结果存储在 db.outputs 中
    """
    # 定义物理常数
    water_density = 1  # 水的密度 kg/m³ (简化值)
    gravity = 9.8  # 重力加速度 m/s²

    # 获取输入参数
    volume = db.inputs.volume  # 物体总体积 m³
    height = db.inputs.height  # 物体高度 m
    z_position = db.inputs.z_position  # 物体Z轴位置 m
    rotation_angles = np.deg2rad(db.inputs.rotation)  # 将角度转换为弧度

    # 计算浸没体积
    submerged_height = calculate_submerged_height(z_position, height)  # 计算浸没高度比例
    submerged_volume = volume * submerged_height  # 浸没体积

    # 根据阿基米德原理计算浮力大小
    buoyancy_force = water_density * submerged_volume * gravity

    # 创建各轴的旋转矩阵
    # 按照 Roll-Pitch-Yaw 的顺序进行旋转变换
    roll_matrix = create_rotation_matrix_x(rotation_angles[0])    # 绕X轴旋转（横滚）
    pitch_matrix = create_rotation_matrix_y(rotation_angles[1])  # 绕Y轴旋转（俯仰）
    yaw_matrix = create_rotation_matrix_z(rotation_angles[2])    # 绕Z轴旋转（偏航）

    # 组合旋转矩阵：先横滚，再俯仰，最后偏航
    rotation_matrix = roll_matrix @ pitch_matrix @ yaw_matrix

    # 创建浮力向量（原始浮力只在Z方向）并进行旋转变换
    buoyancy_vector = np.array([0, 0, buoyancy_force])  # 垂直向上的浮力向量
    rotated_buoyancy_vector = np.matmul(rotation_matrix, buoyancy_vector)  # 旋转后的浮力向量

    # 分配输出结果到各个方向分量
    # 注意：X和Y方向取负值，可能是为了适应特定的坐标系约定
    db.outputs.x_force = -rotated_buoyancy_vector[0, 0]  # X方向浮力分量
    db.outputs.y_force = -rotated_buoyancy_vector[0, 1]  # Y方向浮力分量
    db.outputs.z_force = rotated_buoyancy_vector[0, 2]   # Z方向浮力分量

def calculate_submerged_height(z_position, height):
    """
    根据物体的Z轴位置计算浸没高度比例

    假设水面位于Z=0处，物体中心在z_position位置

    参数:
        z_position: 物体中心的Z轴位置 (m)
                   正值表示在水面上方，负值表示在水面下方
        height: 物体的总高度 (m)

    返回:
        float: 浸没高度比例 (0.0 到 height)
               0.0 表示完全在水面上方
               height 表示完全浸没

    计算逻辑:
        - 如果物体底部在水面上方：浸没高度 = 0
        - 如果物体顶部在水面下方：浸没高度 = 物体总高度
        - 如果物体部分浸没：浸没高度 = 水面下方的部分高度
    """
    center_of_h = height / 2  # 物体中心到顶部/底部的距离

    if z_position >= center_of_h:
        # 物体完全在水面上方
        return 0.0
    elif z_position < -center_of_h:
        # 物体完全在水面下方
        return height
    else:
        # 物体部分浸没
        return center_of_h - z_position

def create_rotation_matrix_x(angle):
    """
    创建绕X轴旋转的旋转矩阵（横滚角 Roll）

    X轴旋转矩阵用于表示物体绕X轴的旋转，通常对应横滚运动

    参数:
        angle: 绕X轴的旋转角度 (弧度)
               正值表示右手定则方向的旋转

    返回:
        numpy.matrix: 3x3的旋转矩阵

    旋转矩阵形式:
        [1    0         0    ]
        [0  cos(θ)  -sin(θ)]
        [0  sin(θ)   cos(θ)]
    """
    return np.matrix([
        [1, 0, 0],
        [0, math.cos(angle), -math.sin(angle)],
        [0, math.sin(angle), math.cos(angle)]
    ])

def create_rotation_matrix_y(angle):
    """
    创建绕Y轴旋转的旋转矩阵（俯仰角 Pitch）

    Y轴旋转矩阵用于表示物体绕Y轴的旋转，通常对应俯仰运动

    参数:
        angle: 绕Y轴的旋转角度 (弧度)
               正值表示右手定则方向的旋转

    返回:
        numpy.matrix: 3x3的旋转矩阵

    旋转矩阵形式:
        [ cos(θ)  0  sin(θ)]
        [   0     1    0   ]
        [-sin(θ)  0  cos(θ)]
    """
    return np.matrix([
        [math.cos(angle), 0, math.sin(angle)],
        [0, 1, 0],
        [-math.sin(angle), 0, math.cos(angle)]
    ])

def create_rotation_matrix_z(angle):
    """
    创建绕Z轴旋转的旋转矩阵（偏航角 Yaw）

    Z轴旋转矩阵用于表示物体绕Z轴的旋转，通常对应偏航运动

    参数:
        angle: 绕Z轴的旋转角度 (弧度)
               正值表示右手定则方向的旋转

    返回:
        numpy.matrix: 3x3的旋转矩阵

    旋转矩阵形式:
        [cos(θ)  -sin(θ)  0]
        [sin(θ)   cos(θ)  0]
        [  0        0     1]
    """
    return np.matrix([
        [math.cos(angle), -math.sin(angle), 0],
        [math.sin(angle), math.cos(angle), 0],
        [0, 0, 1]
    ])
