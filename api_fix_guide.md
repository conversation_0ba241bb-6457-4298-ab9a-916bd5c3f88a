# Isaac Sim 4.5 API 修复指南

## 问题描述

在Isaac Sim 4.5中，`CreateNode`命令的API发生了变化，不再接受`usd_context_name`参数。

### 错误信息
```
TypeError: CreateNodeCommand.__init__() got an unexpected keyword argument 'usd_context_name'
```

## 解决方案

### 修复前（错误的代码）
```python
omni.kit.commands.execute('CreateNode',
    usd_context_name='',  # ❌ 这个参数在4.5中不再支持
    graph_path='/ActionGraph',
    node_type='omni.graph.action.ActionGraph',
    node_name='ActionGraph'
)
```

### 修复后（正确的代码）
```python
omni.kit.commands.execute('CreateNode',
    graph_path='/ActionGraph',  # ✅ 直接使用graph_path
    node_type='omni.graph.action.ActionGraph',
    node_name='ActionGraph'
)
```

## 已修复的文件

我已经修复了以下文件中的所有`CreateNode`调用：

1. **`underwater_action_graph.py`** - 完整的Action Graph构建器
2. **`isaac_sim_launcher.py`** - 简化的启动脚本
3. **`simple_test.py`** - 新的测试脚本

## 测试步骤

### 1. 运行简化测试
首先运行简化的测试脚本来验证API修复：

```python
# 在Isaac Sim Script Editor中运行
exec(open('simple_test.py').read())
```

### 2. 验证基本功能
测试脚本会创建：
- 基本的Action Graph
- 常量节点
- Print节点
- Python脚本节点
- 节点连接

### 3. 检查结果
- 打开 **Window → Visual Scripting → Action Graph**
- 选择 `/ActionGraph`
- 查看创建的节点
- 点击播放按钮观察执行

### 4. 运行完整版本
如果简化测试成功，再运行完整版本：

```python
# 在Isaac Sim Script Editor中运行
from underwater_action_graph import UnderwaterActionGraphBuilder

builder = UnderwaterActionGraphBuilder()
builder.build_underwater_simulation_graph("/World/ROV")
```

## 其他可能的API变化

### 节点类型名称
某些节点类型名称可能也发生了变化。如果遇到节点类型不存在的错误：

```python
# 查看可用的节点类型
import omni.graph.core as og
available_types = og.get_node_types()
print("可用节点类型:", available_types)
```

### 常见节点类型映射
```python
# 如果遇到节点类型错误，尝试以下替代：
node_type_mapping = {
    'omni.graph.nodes.ConstantDouble': 'omni.graph.nodes.ConstantFloat',
    'omni.graph.nodes.GamepadInput': 'omni.graph.ui.GamepadInput',
    'omni.graph.nodes.ApplyForce': 'omni.isaac.core_nodes.ApplyForce',
    # 根据实际情况添加更多映射
}
```

### 属性名称变化
某些节点的属性名称可能也发生了变化：

```python
# 检查节点的可用属性
node = og.get_node_by_path("/ActionGraph/MyNode")
if node:
    attributes = node.get_attributes()
    for attr in attributes:
        print(f"属性: {attr.get_name()}, 类型: {attr.get_type()}")
```

## 调试技巧

### 1. 启用详细日志
```python
import carb
carb.settings.get_settings().set("/log/level", "verbose")
```

### 2. 捕获异常
```python
try:
    omni.kit.commands.execute('CreateNode', ...)
except Exception as e:
    print(f"创建节点失败: {e}")
    print(f"错误类型: {type(e)}")
```

### 3. 检查API文档
```python
# 查看CreateNode命令的帮助
help(omni.kit.commands.execute)
```

## 版本兼容性

### Isaac Sim 4.0.x
```python
# 旧版本需要usd_context_name参数
omni.kit.commands.execute('CreateNode',
    usd_context_name='',
    graph_path='/ActionGraph',
    node_type='omni.graph.action.ActionGraph',
    node_name='ActionGraph'
)
```

### Isaac Sim 4.5+
```python
# 新版本不需要usd_context_name参数
omni.kit.commands.execute('CreateNode',
    graph_path='/ActionGraph',
    node_type='omni.graph.action.ActionGraph',
    node_name='ActionGraph'
)
```

### 兼容性代码
```python
import omni.isaac.version as version

def create_node_compatible(**kwargs):
    """兼容不同版本的节点创建"""
    if version.get_version() >= "4.5.0":
        # 移除usd_context_name参数
        kwargs.pop('usd_context_name', None)
    
    omni.kit.commands.execute('CreateNode', **kwargs)
```

## 总结

主要修复点：
1. ✅ 移除所有`CreateNode`调用中的`usd_context_name`参数
2. ✅ 保持其他参数不变
3. ✅ 添加错误处理和日志
4. ✅ 创建测试脚本验证修复

现在您可以安全地运行修复后的脚本了！
