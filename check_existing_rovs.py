"""
Check Existing ROVs in Scene
Find all ROV objects currently in the Isaac Sim scene
"""

import omni.usd
from pxr import Usd

def find_all_rovs():
    """Find all ROV objects in the current scene"""
    print("🔍 Scanning scene for existing ROVs...")
    
    try:
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ No stage found")
            return []
        
        found_rovs = []
        
        # Search for common ROV paths
        common_rov_paths = [
            "/World/ROV",
            "/World/ROV_Main", 
            "/World/ROV_Scout",
            "/World/ROV_1",
            "/World/ROV_2", 
            "/World/ROV_3",
            "/World/ROV_Test",
            "/World/Cube",  # Sometimes ROVs are just named Cube
        ]
        
        print("\n📋 Checking common ROV paths:")
        for path in common_rov_paths:
            prim = stage.GetPrimAtPath(path)
            if prim.IsValid():
                # Get transform if available
                xform_attr = prim.GetAttribute("xformOp:translate")
                position = "Unknown"
                if xform_attr:
                    pos_value = xform_attr.Get()
                    if pos_value:
                        position = f"({pos_value[0]:.1f}, {pos_value[1]:.1f}, {pos_value[2]:.1f})"
                
                # Get size if it's a cube
                size = "Unknown"
                size_attr = prim.GetAttribute("size")
                if size_attr:
                    size_value = size_attr.Get()
                    if size_value:
                        size = f"{size_value:.1f}m"
                
                found_rovs.append({
                    "path": path,
                    "position": position,
                    "size": size,
                    "type": prim.GetTypeName()
                })
                print(f"  ✅ Found: {path} at {position}, size: {size}, type: {prim.GetTypeName()}")
        
        # Also search the entire /World tree for any objects that might be ROVs
        print("\n🌍 Scanning entire /World tree:")
        world_prim = stage.GetPrimAtPath("/World")
        if world_prim.IsValid():
            for child in world_prim.GetChildren():
                child_path = str(child.GetPath())
                if child_path not in [rov["path"] for rov in found_rovs]:
                    # Check if it looks like it could be a ROV (has physics components)
                    if child.HasAPI("UsdPhysics.RigidBodyAPI") or "ROV" in child_path.upper() or "CUBE" in child_path.upper():
                        # Get position
                        xform_attr = child.GetAttribute("xformOp:translate")
                        position = "Unknown"
                        if xform_attr:
                            pos_value = xform_attr.Get()
                            if pos_value:
                                position = f"({pos_value[0]:.1f}, {pos_value[1]:.1f}, {pos_value[2]:.1f})"
                        
                        # Get size
                        size = "Unknown"
                        size_attr = child.GetAttribute("size")
                        if size_attr:
                            size_value = size_attr.Get()
                            if size_value:
                                size = f"{size_value:.1f}m"
                        
                        found_rovs.append({
                            "path": child_path,
                            "position": position,
                            "size": size,
                            "type": child.GetTypeName()
                        })
                        print(f"  🔍 Found potential ROV: {child_path} at {position}, size: {size}, type: {child.GetTypeName()}")
        
        print(f"\n📊 Total ROVs found: {len(found_rovs)}")
        
        if found_rovs:
            print("\n📋 Summary of all ROVs:")
            for i, rov in enumerate(found_rovs, 1):
                print(f"  {i}. {rov['path']}")
                print(f"     Position: {rov['position']}")
                print(f"     Size: {rov['size']}")
                print(f"     Type: {rov['type']}")
        else:
            print("❌ No ROVs found in scene")
        
        return found_rovs
        
    except Exception as e:
        print(f"❌ Error scanning for ROVs: {e}")
        import traceback
        traceback.print_exc()
        return []


def main():
    """Main function to check ROVs"""
    print("=" * 60)
    print("🤖 ROV DETECTION SYSTEM")
    print("=" * 60)
    
    rovs = find_all_rovs()
    
    if rovs:
        print(f"\n✅ Found {len(rovs)} ROV(s) in the scene!")
        print("\nTo include all ROVs in the physics system, we need to:")
        print("1. Update the ROV configuration list")
        print("2. Create physics scripts for each ROV")
        print("3. Add monitoring for all ROVs")
        
        print(f"\n🔧 Recommended ROV paths for physics system:")
        for rov in rovs:
            print(f"  \"{rov['path']}\",")
            
    else:
        print("\n❌ No ROVs found!")
        print("You may need to:")
        print("1. Create ROVs first")
        print("2. Check if they're in a different location")
        print("3. Run: exec(open('minimal_underwater_test.py').read())")
    
    return rovs


if __name__ == "__main__":
    main()
