"""
Isaac Sim 4.5 Underwater Simulation Launcher
Complete setup for underwater ROV simulation with Action Graph
"""

import asyncio
import omni.graph.core as og
import omni.kit.commands
import omni.usd
from pxr import Gf, UsdGeom, UsdPhysics
import carb


class IsaacSimUnderwaterSetup:
    """Complete Isaac Sim underwater simulation setup"""
    
    def __init__(self):
        self.stage = omni.usd.get_context().get_stage()
        self.graph_path = "/ActionGraph"
        
    async def setup_complete_simulation(self):
        """Setup complete underwater simulation environment"""
        carb.log_info("Setting up Isaac Sim underwater simulation environment...")
        
        # 1. Create basic scene
        await self.create_basic_scene()
        
        # 2. Create ROV model
        await self.create_rov_model()
        
        # 3. Setup physics environment
        await self.setup_physics()
        
        # 4. Create Action Graph
        await self.create_action_graph()
        
        carb.log_info("Underwater simulation environment setup complete!")
    
    async def create_basic_scene(self):
        """Create basic scene structure"""
        # Clear existing scene
        omni.kit.commands.execute('DeletePrims', paths=['/World'])
        
        # Create World
        omni.kit.commands.execute('CreatePrim', prim_type='Xform', prim_path='/World')
        
        # Add default lighting
        omni.kit.commands.execute('CreatePrim', 
            prim_type='DistantLight', 
            prim_path='/World/DistantLight'
        )
        
        # Setup physics scene
        physics_scene_path = "/World/PhysicsScene"
        omni.kit.commands.execute('CreatePrim',
            prim_type='PhysicsScene',
            prim_path=physics_scene_path
        )
        
        # Set gravity vector
        physics_scene = UsdPhysics.Scene.Get(self.stage, physics_scene_path)
        physics_scene.CreateGravityDirectionAttr().Set(Gf.Vec3f(0.0, 0.0, -1.0))
        physics_scene.CreateGravityMagnitudeAttr().Set(9.8)
        
        carb.log_info("Basic scene created")
    
    async def create_rov_model(self):
        """Create ROV model"""
        rov_path = "/World/ROV"
        
        # Create ROV main body (cube)
        omni.kit.commands.execute('CreateMeshPrimWithDefaultXform',
            prim_type='Cube',
            prim_path=rov_path
        )
        
        # Set ROV dimensions
        cube_prim = self.stage.GetPrimAtPath(rov_path)
        cube_geom = UsdGeom.Cube(cube_prim)
        cube_geom.CreateSizeAttr().Set(2.0)  # 2m cube
        
        # Add rigid body properties
        omni.kit.commands.execute('AddPhysicsComponent',
            usd_prim=cube_prim,
            component='RigidBodyAPI'
        )
        
        # Set mass
        rigid_body = UsdPhysics.RigidBodyAPI(cube_prim)
        mass_api = UsdPhysics.MassAPI.Apply(cube_prim)
        mass_api.CreateMassAttr().Set(1000.0)  # 1000kg
        
        # Add collision
        omni.kit.commands.execute('AddPhysicsComponent',
            usd_prim=cube_prim,
            component='CollisionAPI'
        )
        
        # Set initial position (near water surface)
        xform = UsdGeom.Xformable(cube_prim)
        xform.AddTranslateOp().Set(Gf.Vec3d(0.0, 0.0, 0.0))
        
        carb.log_info("ROV model created")
    
    async def setup_physics(self):
        """Setup physics environment"""
        # Create water surface reference plane (for visualization)
        water_surface_path = "/World/WaterSurface"
        omni.kit.commands.execute('CreateMeshPrimWithDefaultXform',
            prim_type='Plane',
            prim_path=water_surface_path
        )
        
        # Set water surface size and position
        plane_prim = self.stage.GetPrimAtPath(water_surface_path)
        plane_geom = UsdGeom.Plane(plane_prim)
        
        xform = UsdGeom.Xformable(plane_prim)
        xform.AddScaleOp().Set(Gf.Vec3f(50.0, 50.0, 1.0))  # 50x50m water surface
        xform.AddTranslateOp().Set(Gf.Vec3d(0.0, 0.0, 0.0))  # Z=0 is water surface
        
        carb.log_info("Physics environment setup complete")
    
    async def create_action_graph(self):
        """Create Action Graph using Controller API"""
        # Delete existing graph
        try:
            omni.kit.commands.execute('DeletePrims', paths=[self.graph_path])
        except:
            pass
        
        # Create Action Graph with simplified buoyancy calculation
        keys = og.Controller.Keys
        try:
            (graph, nodes_created, _, _) = og.Controller.edit(
                {"graph_path": self.graph_path, "evaluator_name": "execution"},
                {
                    keys.CREATE_NODES: [
                        ("ActionGraph", "omni.graph.action.ActionGraph"),
                        ("OnTick", "omni.graph.action.OnTick"),
                        ("GetTransform", "omni.graph.nodes.GetPrimLocalToWorldTransform"),
                        ("ExtractZ", "omni.graph.nodes.ExtractComponent"),
                        ("BuoyancyScript", "omni.graph.scriptnode.ScriptNode"),
                        ("ApplyForce", "omni.graph.nodes.ApplyForce"),
                    ],
                },
            )
            
            if graph and len(nodes_created) == 6:
                # Get node references
                on_tick = nodes_created[1]
                get_transform = nodes_created[2]
                extract_z = nodes_created[3]
                buoyancy_script = nodes_created[4]
                apply_force = nodes_created[5]
                
                # Configure nodes
                og.Controller.edit(
                    graph,
                    {
                        keys.SET_VALUES: [
                            # Set target prim for transform
                            (get_transform.get_attribute("inputs:prim"), "/World/ROV"),
                            # Set extract component index (Z = 2)
                            (extract_z.get_attribute("inputs:index"), 2),
                            # Set target prim for force application
                            (apply_force.get_attribute("inputs:prim"), "/World/ROV"),
                            # Set buoyancy script
                            (buoyancy_script.get_attribute("inputs:script"), self.get_buoyancy_script()),
                        ],
                    },
                )
                
                # Connect nodes
                og.Controller.edit(
                    graph,
                    {
                        keys.CONNECT: [
                            # Execution flow
                            (on_tick.get_attribute("outputs:tick"), 
                             get_transform.get_attribute("inputs:execIn")),
                            
                            # Data flow
                            (get_transform.get_attribute("outputs:translation"), 
                             extract_z.get_attribute("inputs:vector")),
                            (extract_z.get_attribute("outputs:component"), 
                             buoyancy_script.get_attribute("inputs:z_position")),
                            (buoyancy_script.get_attribute("outputs:force"), 
                             apply_force.get_attribute("inputs:force")),
                        ],
                    },
                )
                
                carb.log_info("Action Graph created successfully")
            else:
                carb.log_error("Failed to create all nodes")
                
        except Exception as e:
            carb.log_error(f"Error creating Action Graph: {e}")
    
    def get_buoyancy_script(self):
        """Get the buoyancy calculation script"""
        return '''
def setup(db):
    db.volume = 8.0  # 2x2x2 cube volume
    db.water_density = 1000.0  # kg/m³
    db.gravity = 9.8  # m/s²
    print("Buoyancy script initialized")

def compute(db):
    # Get Z position
    z_pos = db.inputs.z_position
    
    # Calculate buoyancy force
    if z_pos < 0:  # Below water surface
        # Calculate submerged volume
        submerged_height = min(2.0, abs(z_pos))  # ROV height is 2m
        submerged_ratio = submerged_height / 2.0
        submerged_volume = db.volume * submerged_ratio
        
        # Calculate buoyancy force (upward)
        buoyancy_force = db.water_density * submerged_volume * db.gravity
        
        # Apply force in Z direction (upward)
        db.outputs.force = [0.0, 0.0, buoyancy_force]
    else:  # Above water surface
        db.outputs.force = [0.0, 0.0, 0.0]
'''


async def main():
    """Main function"""
    setup = IsaacSimUnderwaterSetup()
    await setup.setup_complete_simulation()
    
    print("=" * 60)
    print("Isaac Sim Underwater Simulation Setup Complete!")
    print("=" * 60)
    print("Scene contains:")
    print("- ROV model (/World/ROV)")
    print("- Water surface reference plane (/World/WaterSurface)")
    print("- Physics environment setup")
    print("- Simplified buoyancy calculation Action Graph")
    print()
    print("Usage:")
    print("1. Click Play button to start simulation")
    print("2. Select ROV in Scene Hierarchy")
    print("3. Use Transform tool to move ROV below water surface")
    print("4. Observe buoyancy force effect")
    print("5. Check Action Graph editor for node connections")
    print()
    print("Next steps:")
    print("- Add gamepad control")
    print("- Integrate complete script modules")
    print("- Add more complex ROV model")


# Run in Isaac Sim
if __name__ == "__main__":
    asyncio.ensure_future(main())
