"""
Run ROV Detection
Check what ROVs exist in the scene and then update the system accordingly
"""

print("🔍 Checking existing ROVs in scene...")
exec(open('check_existing_rovs.py').read())

print("\n" + "="*60)
print("🚀 NEXT: UPDATE SYSTEM FOR ALL ROVS")
print("="*60)
print("After identifying all ROVs, run:")
print("exec(open('create_multi_rov_system.py').read())")
print("This will create a system that includes ALL your ROVs!")
