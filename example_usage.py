"""
<PERSON> Sim 水下仿真脚本使用示例
演示如何在Isaac Sim环境中应用scripts目录下的各个模块
"""

import numpy as np
from scripts import quat_to_euler, buoyancy_forces, damping, controller, linear_angular_control

class UnderwaterSimulationExample:
    """
    水下仿真示例类
    展示如何组合使用各个脚本模块来实现完整的水下机器人仿真
    """
    
    def __init__(self):
        """初始化仿真参数"""
        # 物体物理参数
        self.volume = 1.0  # 体积 m³
        self.height = 2.0  # 高度 m
        self.max_damping = 10.0  # 最大阻尼系数
        
        # 创建模拟的数据库对象
        self.db_quat = self.create_db_object()
        self.db_buoyancy = self.create_db_object()
        self.db_damping = self.create_db_object()
        self.db_controller = self.create_db_object()
        self.db_thruster = self.create_db_object()
        
        # 初始化各个模块
        self.setup_modules()
    
    def create_db_object(self):
        """创建模拟的数据库对象"""
        class DB:
            def __init__(self):
                self.inputs = type('inputs', (), {})()
                self.outputs = type('outputs', (), {})()
        return DB()
    
    def setup_modules(self):
        """初始化所有模块"""
        quat_to_euler.setup(self.db_quat)
        buoyancy_forces.setup(self.db_buoyancy)
        damping.setup(self.db_damping)
        controller.setup(self.db_controller)
        linear_angular_control.setup(self.db_thruster)
        
        print("所有模块初始化完成")
    
    def simulate_frame(self, quaternion, position, joystick_input):
        """
        模拟单帧计算
        
        参数:
            quaternion: 四元数姿态 [x, y, z, w]
            position: 位置 [x, y, z]
            joystick_input: 操纵杆输入 [x_stick, y_stick, dive_button]
        
        返回:
            dict: 包含所有计算结果的字典
        """
        results = {}
        
        # 1. 四元数转欧拉角
        self.db_quat.inputs.quaternion = quaternion
        quat_to_euler.compute(self.db_quat)
        euler_angles = self.db_quat.outputs.rotation
        results['euler_angles'] = euler_angles
        
        # 2. 计算浮力
        self.db_buoyancy.inputs.volume = self.volume
        self.db_buoyancy.inputs.height = self.height
        self.db_buoyancy.inputs.z_position = position[2]
        self.db_buoyancy.inputs.rotation = euler_angles
        buoyancy_forces.compute(self.db_buoyancy)
        
        buoyancy_force = [
            self.db_buoyancy.outputs.x_force,
            self.db_buoyancy.outputs.y_force,
            self.db_buoyancy.outputs.z_force
        ]
        results['buoyancy_force'] = buoyancy_force
        
        # 3. 计算阻尼
        self.db_damping.inputs.z_position = position[2]
        self.db_damping.inputs.max_damping = self.max_damping
        self.db_damping.inputs.floating_obj_height = self.height
        damping.compute(self.db_damping)
        
        damping_coeffs = {
            'linear': self.db_damping.outputs.linear_damping,
            'angular': self.db_damping.outputs.angular_damping
        }
        results['damping'] = damping_coeffs
        
        # 4. PID控制器（以roll角为例）
        self.db_controller.inputs.orientation = euler_angles[0]  # roll角
        self.db_controller.inputs.dive_force = 100.0 if joystick_input[2] > 0.5 else 0.0
        controller.compute(self.db_controller)
        
        control_forces = {
            'force': self.db_controller.outputs.force,
            'minus_force': self.db_controller.outputs.minus_force
        }
        results['control_forces'] = control_forces
        
        # 5. 推进器控制
        self.db_thruster.inputs.x_stick = joystick_input[0]
        self.db_thruster.inputs.y_stick = joystick_input[1]
        linear_angular_control.compute(self.db_thruster)
        
        thruster_forces = {
            'left_front': self.db_thruster.outputs.left_front,
            'right_front': self.db_thruster.outputs.right_front,
            'left_back': self.db_thruster.outputs.left_back,
            'right_back': self.db_thruster.outputs.right_back
        }
        results['thruster_forces'] = thruster_forces
        
        return results
    
    def print_results(self, results):
        """打印仿真结果"""
        print("\n=== 仿真结果 ===")
        print(f"欧拉角 (度): Roll={results['euler_angles'][0]:.2f}, "
              f"Pitch={results['euler_angles'][1]:.2f}, "
              f"Yaw={results['euler_angles'][2]:.2f}")
        
        print(f"浮力 (N): X={results['buoyancy_force'][0]:.2f}, "
              f"Y={results['buoyancy_force'][1]:.2f}, "
              f"Z={results['buoyancy_force'][2]:.2f}")
        
        print(f"阻尼系数: 线性={results['damping']['linear']:.3f}, "
              f"角度={results['damping']['angular']:.3f}")
        
        print(f"控制力 (N): {results['control_forces']['force']}")
        
        print("推进器力 (N):")
        for name, force in results['thruster_forces'].items():
            print(f"  {name}: {force}")


def main():
    """主函数 - 演示脚本使用"""
    print("Isaac Sim 水下仿真脚本使用示例")
    print("=" * 50)
    
    # 创建仿真实例
    sim = UnderwaterSimulationExample()
    
    # 模拟数据
    test_cases = [
        {
            'name': '水面漂浮状态',
            'quaternion': [0.0, 0.0, 0.0, 1.0],  # 无旋转
            'position': [0.0, 0.0, 0.5],  # 部分在水面上
            'joystick': [0.0, 0.0, 0.0]  # 无操作
        },
        {
            'name': '完全浸没状态',
            'quaternion': [0.1, 0.0, 0.0, 0.995],  # 轻微倾斜
            'position': [0.0, 0.0, -2.0],  # 完全在水下
            'joystick': [0.5, 0.8, 1.0]  # 右转前进并下潜
        },
        {
            'name': '倾斜漂浮状态',
            'quaternion': [0.0, 0.259, 0.0, 0.966],  # 30度俯仰
            'position': [0.0, 0.0, 0.0],  # 在水面
            'joystick': [-0.3, -0.5, 0.0]  # 左转后退
        }
    ]
    
    # 运行测试用例
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {case['name']}")
        print("-" * 30)
        
        results = sim.simulate_frame(
            case['quaternion'],
            case['position'],
            case['joystick']
        )
        
        sim.print_results(results)
    
    print("\n仿真演示完成！")
    print("\n在Isaac Sim中的实际应用:")
    print("1. 将这些脚本加载到Action Graph的Python Script节点中")
    print("2. 连接物体的Transform、Rigid Body等组件作为输入")
    print("3. 将计算结果连接到Apply Forces节点")
    print("4. 配置游戏手柄输入节点")
    print("5. 运行仿真并观察效果")


if __name__ == "__main__":
    main()
