# Isaac Sim 4.5 Action Graph Python API Guide

## API Changes in Isaac Sim 4.5

Isaac Sim 4.5 introduced significant changes to the Action Graph Python API. The old `CreateNode` command approach has been replaced with the **OmniGraph Controller API**.

## Correct API Usage

### Old API (Isaac Sim 4.0.x) - ❌ No longer works
```python
omni.kit.commands.execute('CreateNode',
    usd_context_name='',  # This parameter no longer exists
    graph_path='/ActionGraph',
    node_type='omni.graph.action.ActionGraph',
    node_name='ActionGraph'
)
```

### New API (Isaac Sim 4.5+) - ✅ Correct approach
```python
import omni.graph.core as og

keys = og.Controller.Keys
(graph, nodes_created, _, _) = og.Controller.edit(
    {"graph_path": "/ActionGraph", "evaluator_name": "execution"},
    {
        keys.CREATE_NODES: [
            ("ActionGraph", "omni.graph.action.ActionGraph"),
            ("OnTick", "omni.graph.action.OnTick"),
            ("ConstantDouble", "omni.graph.nodes.ConstantDouble"),
        ],
    },
)
```

## Core Controller API Methods

### 1. Creating Graphs and Nodes
```python
keys = og.Controller.Keys
(graph, nodes_created, _, _) = og.Controller.edit(
    {"graph_path": "/ActionGraph", "evaluator_name": "execution"},
    {
        keys.CREATE_NODES: [
            ("NodeName", "node.type.name"),
            # Add more nodes...
        ],
    },
)
```

### 2. Setting Node Attributes
```python
og.Controller.edit(
    graph,
    {
        keys.SET_VALUES: [
            (node.get_attribute("inputs:attributeName"), value),
            # Set more attributes...
        ],
    },
)
```

### 3. Connecting Nodes
```python
og.Controller.edit(
    graph,
    {
        keys.CONNECT: [
            (source_node.get_attribute("outputs:outputName"), 
             target_node.get_attribute("inputs:inputName")),
            # Add more connections...
        ],
    },
)
```

## Common Node Types

### Input Nodes
- `omni.graph.action.OnTick` - Execution trigger
- `omni.graph.nodes.GamepadInput` - Gamepad input
- `omni.graph.nodes.KeyboardInput` - Keyboard input

### Data Nodes
- `omni.graph.nodes.GetPrimLocalToWorldTransform` - Get transform
- `omni.graph.nodes.ConstantDouble` - Constant value
- `omni.graph.nodes.ConstantVector3` - Constant vector

### Math Nodes
- `omni.graph.nodes.ExtractComponent` - Extract vector component
- `omni.graph.nodes.MakeArray` - Create array
- `omni.graph.nodes.VectorMath` - Vector operations

### Script Nodes
- `omni.graph.scriptnode.ScriptNode` - Python script

### Output Nodes
- `omni.graph.nodes.ApplyForce` - Apply physics force
- `omni.graph.nodes.Print` - Debug output

## Complete Example

```python
import omni.graph.core as og
import carb

def create_simple_graph():
    graph_path = "/TestGraph"
    
    # Delete existing graph
    try:
        omni.kit.commands.execute('DeletePrims', paths=[graph_path])
    except:
        pass
    
    # Create graph and nodes
    keys = og.Controller.Keys
    (graph, nodes_created, _, _) = og.Controller.edit(
        {"graph_path": graph_path, "evaluator_name": "execution"},
        {
            keys.CREATE_NODES: [
                ("ActionGraph", "omni.graph.action.ActionGraph"),
                ("OnTick", "omni.graph.action.OnTick"),
                ("Constant", "omni.graph.nodes.ConstantDouble"),
                ("Script", "omni.graph.scriptnode.ScriptNode"),
                ("Print", "omni.graph.nodes.Print"),
            ],
        },
    )
    
    if graph and len(nodes_created) == 5:
        on_tick, constant, script, print_node = nodes_created[1:]
        
        # Set attributes
        og.Controller.edit(
            graph,
            {
                keys.SET_VALUES: [
                    (constant.get_attribute("inputs:value"), 42.0),
                    (script.get_attribute("inputs:script"), '''
def setup(db):
    pass

def compute(db):
    db.outputs.result = db.inputs.input_value * 2
'''),
                ],
            },
        )
        
        # Connect nodes
        og.Controller.edit(
            graph,
            {
                keys.CONNECT: [
                    (on_tick.get_attribute("outputs:tick"), 
                     print_node.get_attribute("inputs:execIn")),
                    (constant.get_attribute("outputs:value"), 
                     script.get_attribute("inputs:input_value")),
                    (script.get_attribute("outputs:result"), 
                     print_node.get_attribute("inputs:value")),
                ],
            },
        )
        
        carb.log_info("Graph created successfully")
        return True
    
    return False
```

## Error Handling

### Common Errors and Solutions

1. **"CreateNodeCommand.__init__() got an unexpected keyword argument"**
   - Solution: Use Controller API instead of CreateNode command

2. **"Node type not found"**
   - Check available node types: `og.get_node_types()`
   - Verify correct node type string

3. **"Attribute not found"**
   - Check node attributes: `node.get_attributes()`
   - Verify input/output attribute names

### Debug Tips
```python
# List available node types
node_types = og.get_node_types()
print("Available node types:", node_types)

# Check node attributes
if node:
    attributes = node.get_attributes()
    for attr in attributes:
        print(f"Attribute: {attr.get_name()}, Type: {attr.get_type()}")

# Enable verbose logging
import carb
carb.settings.get_settings().set("/log/level", "verbose")
```

## Testing Your Setup

### Step 1: Run Simple Test
```python
exec(open('simple_test_en.py').read())
```

### Step 2: Check Results
1. Open **Window → Visual Scripting → Action Graph**
2. Select created graph in left panel
3. Verify nodes and connections
4. Click Play to test execution

### Step 3: Run Full Simulation
```python
exec(open('underwater_action_graph_en.py').read())
```

## Best Practices

1. **Always use Controller API** for Isaac Sim 4.5+
2. **Handle errors gracefully** with try-catch blocks
3. **Check node creation success** before proceeding
4. **Use meaningful node names** for debugging
5. **Test incrementally** - start simple, add complexity
6. **Enable logging** for troubleshooting

## Migration from Old API

If you have existing code using the old API:

1. Replace `omni.kit.commands.execute('CreateNode', ...)` with Controller API
2. Remove `usd_context_name` parameters
3. Use batch operations for better performance
4. Update node type names if necessary
5. Test thoroughly with new API

This guide should help you successfully use the Isaac Sim 4.5 Action Graph Python API!
