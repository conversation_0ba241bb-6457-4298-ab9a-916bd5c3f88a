"""
Isaac Sim 4.5 水下仿真启动脚本
在Isaac Sim环境中运行此脚本来创建完整的水下仿真Action Graph
"""

import asyncio
import omni.graph.core as og
import omni.kit.commands
import omni.usd
from pxr import Gf, Sdf, UsdGeom, UsdPhysics
import carb


class IsaacSimUnderwaterSetup:
    """Isaac Sim水下仿真完整设置"""
    
    def __init__(self):
        self.stage = omni.usd.get_context().get_stage()
        self.graph_path = "/ActionGraph"
        
    async def setup_complete_simulation(self):
        """设置完整的水下仿真环境"""
        carb.log_info("开始设置Isaac Sim水下仿真环境...")
        
        # 1. 创建基础场景
        await self.create_basic_scene()
        
        # 2. 创建ROV模型
        await self.create_rov_model()
        
        # 3. 设置物理环境
        await self.setup_physics()
        
        # 4. 创建Action Graph
        await self.create_action_graph()
        
        carb.log_info("水下仿真环境设置完成！")
    
    async def create_basic_scene(self):
        """创建基础场景"""
        # 清理现有场景
        omni.kit.commands.execute('DeletePrims', paths=['/World'])
        
        # 创建World
        omni.kit.commands.execute('CreatePrim', prim_type='Xform', prim_path='/World')
        
        # 添加默认光照
        omni.kit.commands.execute('CreatePrim', 
            prim_type='DistantLight', 
            prim_path='/World/DistantLight'
        )
        
        # 设置重力（模拟水下环境）
        physics_scene_path = "/World/PhysicsScene"
        omni.kit.commands.execute('CreatePrim',
            prim_type='PhysicsScene',
            prim_path=physics_scene_path
        )
        
        # 设置重力向量
        physics_scene = UsdPhysics.Scene.Get(self.stage, physics_scene_path)
        physics_scene.CreateGravityDirectionAttr().Set(Gf.Vec3f(0.0, 0.0, -1.0))
        physics_scene.CreateGravityMagnitudeAttr().Set(9.8)
        
        carb.log_info("基础场景创建完成")
    
    async def create_rov_model(self):
        """创建ROV模型"""
        rov_path = "/World/ROV"
        
        # 创建ROV主体（立方体）
        omni.kit.commands.execute('CreateMeshPrimWithDefaultXform',
            prim_type='Cube',
            prim_path=rov_path
        )
        
        # 设置ROV尺寸
        cube_prim = self.stage.GetPrimAtPath(rov_path)
        cube_geom = UsdGeom.Cube(cube_prim)
        cube_geom.CreateSizeAttr().Set(2.0)  # 2米立方体
        
        # 添加刚体属性
        omni.kit.commands.execute('AddPhysicsComponent',
            usd_prim=cube_prim,
            component='RigidBodyAPI'
        )
        
        # 设置质量
        rigid_body = UsdPhysics.RigidBodyAPI(cube_prim)
        mass_api = UsdPhysics.MassAPI.Apply(cube_prim)
        mass_api.CreateMassAttr().Set(1000.0)  # 1000kg
        
        # 添加碰撞体
        omni.kit.commands.execute('AddPhysicsComponent',
            usd_prim=cube_prim,
            component='CollisionAPI'
        )
        
        # 设置初始位置（在水面附近）
        xform = UsdGeom.Xformable(cube_prim)
        xform.AddTranslateOp().Set(Gf.Vec3d(0.0, 0.0, 0.0))
        
        carb.log_info("ROV模型创建完成")
    
    async def setup_physics(self):
        """设置物理环境"""
        # 这里可以添加水面、海底等环境元素
        # 为简化，我们只设置基本的物理参数
        
        # 创建水面参考平面（可视化用）
        water_surface_path = "/World/WaterSurface"
        omni.kit.commands.execute('CreateMeshPrimWithDefaultXform',
            prim_type='Plane',
            prim_path=water_surface_path
        )
        
        # 设置水面大小和位置
        plane_prim = self.stage.GetPrimAtPath(water_surface_path)
        plane_geom = UsdGeom.Plane(plane_prim)
        
        xform = UsdGeom.Xformable(plane_prim)
        xform.AddScaleOp().Set(Gf.Vec3f(50.0, 50.0, 1.0))  # 50x50米水面
        xform.AddTranslateOp().Set(Gf.Vec3d(0.0, 0.0, 0.0))  # Z=0为水面
        
        # 设置水面材质（半透明蓝色）
        # 这里可以添加材质设置代码
        
        carb.log_info("物理环境设置完成")
    
    async def create_action_graph(self):
        """创建Action Graph"""
        # 删除已存在的图
        try:
            omni.kit.commands.execute('DeletePrims', paths=[self.graph_path])
        except:
            pass
        
        # 创建Action Graph
        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.action.ActionGraph',
            node_name='ActionGraph'
        )
        
        # 获取图对象
        graph = og.get_graph_by_path(self.graph_path)
        
        # 创建简化的节点网络
        await self.create_simplified_graph(graph)
        
        carb.log_info("Action Graph创建完成")
    
    async def create_simplified_graph(self, graph):
        """创建简化的Action Graph节点网络"""
        # 这里创建一个简化版本的节点网络
        # 包含基本的浮力计算和力应用
        
        # 1. 创建On Tick节点（每帧执行）
        tick_node_path = f"{self.graph_path}/OnTick"
        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.action.OnTick',
            node_name='OnTick'
        )

        # 2. 创建获取Transform节点
        transform_node_path = f"{self.graph_path}/GetTransform"
        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.nodes.GetPrimLocalToWorldTransform',
            node_name='GetTransform'
        )
        
        # 设置目标Prim
        transform_node = og.get_node_by_path(transform_node_path)
        prim_attr = transform_node.get_attribute("inputs:prim")
        prim_attr.set("/World/ROV")
        
        # 3. 创建Python脚本节点（浮力计算）
        script_node_path = f"{self.graph_path}/BuoyancyScript"
        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.scriptnode.ScriptNode',
            node_name='BuoyancyScript'
        )
        
        # 设置脚本内容（简化的浮力计算）
        script_node = og.get_node_by_path(script_node_path)
        script_content = '''
def setup(db):
    db.volume = 8.0  # 2x2x2立方体体积
    db.water_density = 1000.0
    db.gravity = 9.8

def compute(db):
    # 获取Z位置
    z_pos = db.inputs.z_position
    
    # 简化的浮力计算
    if z_pos < 0:  # 在水面下
        submerged_volume = db.volume * min(1.0, abs(z_pos) / 2.0)
        buoyancy_force = db.water_density * submerged_volume * db.gravity
        db.outputs.force = [0.0, 0.0, buoyancy_force]
    else:  # 在水面上
        db.outputs.force = [0.0, 0.0, 0.0]
'''
        
        script_attr = script_node.get_attribute("inputs:script")
        script_attr.set(script_content)
        
        # 添加输入输出端口
        script_node.create_attribute("inputs:z_position", og.Type(og.BaseDataType.DOUBLE))
        script_node.create_attribute("outputs:force", og.Type(og.BaseDataType.DOUBLE, 3))
        
        # 4. 创建Apply Force节点
        force_node_path = f"{self.graph_path}/ApplyForce"
        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.nodes.ApplyForce',
            node_name='ApplyForce'
        )
        
        force_node = og.get_node_by_path(force_node_path)
        force_prim_attr = force_node.get_attribute("inputs:prim")
        force_prim_attr.set("/World/ROV")
        
        # 5. 创建Extract Component节点（提取Z位置）
        extract_node_path = f"{self.graph_path}/ExtractZ"
        omni.kit.commands.execute('CreateNode',
            graph_path=self.graph_path,
            node_type='omni.graph.nodes.ExtractComponent',
            node_name='ExtractZ'
        )
        
        extract_node = og.get_node_by_path(extract_node_path)
        index_attr = extract_node.get_attribute("inputs:index")
        index_attr.set(2)  # Z分量
        
        # 6. 连接节点
        # OnTick -> GetTransform
        tick_node = og.get_node_by_path(tick_node_path)
        og.Controller.connect(
            tick_node.get_attribute("outputs:tick"),
            transform_node.get_attribute("inputs:execIn")
        )
        
        # GetTransform -> ExtractZ
        og.Controller.connect(
            transform_node.get_attribute("outputs:translation"),
            extract_node.get_attribute("inputs:vector")
        )
        
        # ExtractZ -> BuoyancyScript
        og.Controller.connect(
            extract_node.get_attribute("outputs:component"),
            script_node.get_attribute("inputs:z_position")
        )
        
        # BuoyancyScript -> ApplyForce
        og.Controller.connect(
            script_node.get_attribute("outputs:force"),
            force_node.get_attribute("inputs:force")
        )
        
        carb.log_info("简化Action Graph节点网络创建完成")


async def main():
    """主函数"""
    setup = IsaacSimUnderwaterSetup()
    await setup.setup_complete_simulation()
    
    print("=" * 60)
    print("Isaac Sim 水下仿真环境设置完成！")
    print("=" * 60)
    print("场景包含:")
    print("- ROV模型 (/World/ROV)")
    print("- 水面参考平面 (/World/WaterSurface)")
    print("- 物理环境设置")
    print("- 简化的浮力计算Action Graph")
    print()
    print("使用方法:")
    print("1. 点击播放按钮开始仿真")
    print("2. 在Scene Hierarchy中选择ROV")
    print("3. 使用Transform工具移动ROV到水面下方观察浮力效果")
    print("4. 在Action Graph编辑器中查看节点连接")
    print()
    print("下一步:")
    print("- 添加游戏手柄控制")
    print("- 集成完整的脚本模块")
    print("- 添加更复杂的ROV模型")


# 在Isaac Sim中运行
if __name__ == "__main__":
    asyncio.ensure_future(main())
