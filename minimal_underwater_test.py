"""
Minimal Underwater Test - Only Verified Node Types
Use only the node types we know work from the basic test
"""

import omni.graph.core as og
import omni.kit.commands
import carb


def create_minimal_underwater_graph():
    """Create minimal underwater graph with only verified nodes"""
    graph_path = "/MinimalUnderwaterGraph"
    
    print("Creating minimal underwater graph with verified nodes only...")
    
    # Delete existing graph
    try:
        omni.kit.commands.execute('DeletePrims', paths=[graph_path])
    except:
        pass
    
    try:
        keys = og.Controller.Keys
        (graph_handle, nodes_created, _, _) = og.Controller.edit(
            {"graph_path": graph_path, "evaluator_name": "execution"},
            {
                keys.CREATE_NODES: [
                    # Only use nodes we know work
                    ("tick", "omni.graph.action.OnTick"),
                    ("underwater_script", "omni.graph.scriptnode.ScriptNode"),
                    ("debug_print", "omni.graph.ui_nodes.PrintText"),
                ],
                keys.SET_VALUES: [
                    ("debug_print.inputs:text", "Minimal Underwater Test"),
                    ("debug_print.inputs:logLevel", "Warning"),
                    ("underwater_script.inputs:script", get_minimal_underwater_script()),
                ],
                keys.CONNECT: [
                    ("tick.outputs:tick", "underwater_script.inputs:execIn"),
                    ("underwater_script.outputs:execOut", "debug_print.inputs:execIn"),
                ],
            },
        )
        
        print(f"✓ Minimal graph created: {graph_handle}")
        print(f"✓ Nodes created: {len(nodes_created)}")
        return True
        
    except Exception as e:
        print(f"✗ Failed to create minimal graph: {e}")
        import traceback
        traceback.print_exc()
        return False


def get_minimal_underwater_script():
    """Minimal underwater physics script"""
    return '''
import math
import omni.usd

def setup(db):
    db.water_density = 1000.0  # kg/m³
    db.gravity = 9.8  # m/s²
    db.rov_volume = 8.0  # m³
    db.rov_height = 2.0  # m
    db.water_level = 0.0  # Water surface at Z=0
    db.frame_count = 0
    print("Minimal underwater physics initialized")

def compute(db):
    db.frame_count += 1
    
    # Simple demonstration - print every 60 frames (about 1 second)
    if db.frame_count % 60 == 0:
        print(f"Underwater physics running - Frame {db.frame_count}")
    
    # Try to get ROV position (if it exists)
    try:
        stage = omni.usd.get_context().get_stage()
        rov_prim = stage.GetPrimAtPath("/World/ROV")
        
        if rov_prim.IsValid():
            # Get transform
            xform = rov_prim.GetAttribute("xformOp:translate")
            if xform:
                position = xform.Get()
                if position:
                    z_pos = position[2]
                    
                    # Calculate simple buoyancy
                    depth = db.water_level - z_pos
                    
                    if depth > 0:
                        # Submerged - calculate buoyancy force
                        submerged_ratio = min(1.0, depth / db.rov_height)
                        submerged_volume = db.rov_volume * submerged_ratio
                        buoyancy_force = db.water_density * submerged_volume * db.gravity
                        
                        # Output force (this would normally go to ApplyForce node)
                        db.outputs.buoyancy_force = buoyancy_force
                        db.outputs.is_submerged = True
                        
                        if db.frame_count % 60 == 0:
                            print(f"ROV at depth {depth:.2f}m, buoyancy force: {buoyancy_force:.1f}N")
                    else:
                        db.outputs.buoyancy_force = 0.0
                        db.outputs.is_submerged = False
                        
                        if db.frame_count % 60 == 0:
                            print(f"ROV above water at Z={z_pos:.2f}m")
    except Exception as e:
        # If we can't access the ROV, just run basic simulation
        if db.frame_count % 60 == 0:
            print(f"Running basic underwater simulation (no ROV found)")
        
        db.outputs.buoyancy_force = 0.0
        db.outputs.is_submerged = False
    
    # Always output something for the execution chain
    db.outputs.frame_count = db.frame_count
'''


def create_with_transform_node():
    """Try to create graph with GetPrimLocalToWorldTransform"""
    graph_path = "/TransformTestGraph"
    
    print("Testing GetPrimLocalToWorldTransform node...")
    
    # Delete existing graph
    try:
        omni.kit.commands.execute('DeletePrims', paths=[graph_path])
    except:
        pass
    
    try:
        keys = og.Controller.Keys
        (graph_handle, nodes_created, _, _) = og.Controller.edit(
            {"graph_path": graph_path, "evaluator_name": "execution"},
            {
                keys.CREATE_NODES: [
                    ("tick", "omni.graph.action.OnTick"),
                    ("get_transform", "omni.graph.nodes.GetPrimLocalToWorldTransform"),
                    ("physics_script", "omni.graph.scriptnode.ScriptNode"),
                    ("debug_print", "omni.graph.ui_nodes.PrintText"),
                ],
                keys.SET_VALUES: [
                    ("get_transform.inputs:prim", "/World/ROV"),
                    ("debug_print.inputs:text", "Transform Test"),
                    ("debug_print.inputs:logLevel", "Warning"),
                    ("physics_script.inputs:script", get_transform_physics_script()),
                ],
                keys.CONNECT: [
                    ("tick.outputs:tick", "get_transform.inputs:execIn"),
                    ("get_transform.outputs:execOut", "physics_script.inputs:execIn"),
                    ("get_transform.outputs:translation", "physics_script.inputs:position"),
                    ("get_transform.outputs:rotation", "physics_script.inputs:rotation"),
                    ("physics_script.outputs:execOut", "debug_print.inputs:execIn"),
                ],
            },
        )
        
        print(f"✓ Transform test graph created: {graph_handle}")
        return True
        
    except Exception as e:
        print(f"✗ Transform test failed: {e}")
        return False


def get_transform_physics_script():
    """Physics script that uses transform inputs"""
    return '''
import math

def setup(db):
    db.water_density = 1000.0
    db.gravity = 9.8
    db.rov_volume = 8.0
    db.rov_height = 2.0
    db.water_level = 0.0
    db.frame_count = 0
    print("Transform-based underwater physics initialized")

def compute(db):
    db.frame_count += 1
    
    # Get position and rotation from transform node
    position = db.inputs.position
    rotation = db.inputs.rotation
    
    if position and len(position) >= 3:
        z_pos = position[2]
        depth = db.water_level - z_pos
        
        if depth > 0:
            # Calculate buoyancy
            submerged_ratio = min(1.0, depth / db.rov_height)
            submerged_volume = db.rov_volume * submerged_ratio
            buoyancy_force = db.water_density * submerged_volume * db.gravity
            
            # Calculate stabilization forces from rotation
            if rotation and len(rotation) >= 4:
                x, y, z, w = rotation
                roll_force = x * -200.0
                pitch_force = y * -200.0
            else:
                roll_force = 0.0
                pitch_force = 0.0
            
            # Output combined forces
            db.outputs.total_force = [roll_force, pitch_force, buoyancy_force]
            db.outputs.is_submerged = True
            
            if db.frame_count % 60 == 0:
                print(f"Depth: {depth:.2f}m, Forces: [{roll_force:.1f}, {pitch_force:.1f}, {buoyancy_force:.1f}]")
        else:
            db.outputs.total_force = [0.0, 0.0, 0.0]
            db.outputs.is_submerged = False
    else:
        db.outputs.total_force = [0.0, 0.0, 0.0]
        db.outputs.is_submerged = False
    
    db.outputs.frame_count = db.frame_count
'''


def main():
    """Main test function"""
    print("=" * 60)
    print("Minimal Underwater Test")
    print("=" * 60)
    
    # Test 1: Minimal graph with only script node
    print("\n1. Testing minimal graph (script only)...")
    if create_minimal_underwater_graph():
        print("✓ Minimal graph test passed")
    else:
        print("✗ Minimal graph test failed")
        return False
    
    # Test 2: Try with transform node
    print("\n2. Testing with GetPrimLocalToWorldTransform...")
    if create_with_transform_node():
        print("✓ Transform node test passed")
    else:
        print("✗ Transform node test failed - but minimal version still works")
    
    print("\n" + "=" * 60)
    print("Tests completed!")
    print("=" * 60)
    print("Next steps:")
    print("1. Open Window → Visual Scripting → Action Graph")
    print("2. Select /MinimalUnderwaterGraph or /TransformTestGraph")
    print("3. Create a cube at /World/ROV with RigidBody component")
    print("4. Click Play to see underwater physics in action")
    print("5. Check console for physics output messages")
    
    return True


if __name__ == "__main__":
    main()
