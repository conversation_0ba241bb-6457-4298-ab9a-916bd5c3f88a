"""
PID控制器模块
实现比例-积分-微分(PID)控制算法，用于水下机器人的姿态控制
"""

def setup(db):
    """
    初始化PID控制器参数和变量

    设置控制器的增益参数、饱和限制和内部状态变量

    参数:
        db: 数据库对象，用于存储控制器参数和状态
            设置的参数包括:
                sat_max: 控制输出的最大饱和限制
                sat_min: 控制输出的最小饱和限制
                kp: 比例增益 - 控制当前误差的响应强度
                ki: 积分增益 - 控制累积误差的响应强度
                kd: 微分增益 - 控制误差变化率的响应强度
                error_integral: 误差积分累积值
                error_prev: 上一次的误差值
                time: 离散化时间步长
    """
    # 控制输出饱和限制设置
    db.sat_max = 1000   # 最大控制输出限制
    db.sat_min = -1000  # 最小控制输出限制

    # PID控制器增益参数设置
    db.kp = 100   # 比例增益：响应当前误差的强度
    db.ki = 10    # 积分增益：消除稳态误差的强度
    db.kd = 0.01  # 微分增益：预测和抑制误差变化的强度

    # PID控制器内部状态变量初始化
    db.error_integral = 0  # 误差积分累积值初始化为0
    db.error_prev = 0      # 前一次误差值初始化为0

    # 控制周期时间步长（约60Hz控制频率）
    db.time = 0.01667  # 时间步长 ≈ 1/60 秒

def compute(db):
    """
    计算PID控制输出，用于姿态控制和潜水力控制

    实现标准的PID控制算法，目标是将姿态角度维持在0度
    控制输出会被限制在饱和范围内，最终力输出包含额外的潜水力分量

    PID控制公式：
    u(t) = Kp*e(t) + Ki*∫e(t)dt + Kd*de(t)/dt

    参数:
        db: 数据库对象，包含输入和输出数据
            输入参数:
                orientation: 当前姿态角度测量值（目标值为0）
                dive_force: 额外的垂直潜水力
            输出参数:
                force: 正向控制力向量 [x,y,z]
                minus_force: 反向控制力向量 [x,y,z]

    返回:
        无直接返回值，结果存储在 db.outputs 中
    """
    # 获取输入参数
    orientation = db.inputs.orientation  # 当前姿态角度
    dive_force = db.inputs.dive_force    # 潜水力

    # 计算控制误差（目标值为0）
    error = 0 - orientation

    # 更新积分项（累积误差）
    db.error_integral += error

    # 计算PID控制输出
    # 比例项：Kp * 当前误差
    # 积分项：Ki * 累积误差 * 时间步长
    # 微分项：Kd * 误差变化率
    control_output = (db.kp * error +
                     db.ki * (db.error_integral) * db.time +
                     db.kd * (error - db.error_prev) / db.time)

    # 控制输出饱和限制
    if control_output > db.sat_max:
        control_output = db.sat_max
    elif control_output < db.sat_min:
        control_output = db.sat_min

    # 更新前一次误差值，用于下次微分计算
    db.error_prev = error

    # 输出控制力向量
    # 正向控制力：控制输出 + 潜水力
    db.outputs.force = [0, 0, control_output + dive_force]
    # 反向控制力：-控制输出 + 潜水力（用于双向控制）
    db.outputs.minus_force = [0, 0, -control_output + dive_force]
