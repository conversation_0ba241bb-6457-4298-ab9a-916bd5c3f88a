# Isaac Sim 4.5 Python Action Graph 完整指南

## 概述
Isaac Sim 4.5 支持使用纯Python代码创建和配置Action Graph，这比GUI操作更加灵活、可重复和易于版本控制。

## 核心优势

### 1. **代码化配置**
- 所有节点和连接都通过代码定义
- 易于版本控制和团队协作
- 可以批量创建复杂的图结构

### 2. **动态生成**
- 根据参数动态创建不同的图结构
- 支持条件逻辑和循环创建节点
- 便于参数化和模板化

### 3. **集成开发**
- 与其他Python代码无缝集成
- 支持自动化测试和CI/CD
- 便于调试和错误处理

## 核心API介绍

### 1. **omni.graph.core (og)**
```python
import omni.graph.core as og

# 获取图对象
graph = og.get_graph_by_path("/ActionGraph")

# 获取节点
node = og.get_node_by_path("/ActionGraph/MyNode")

# 连接节点
og.Controller.connect(source_attr, target_attr)
```

### 2. **omni.kit.commands**
```python
import omni.kit.commands

# 创建节点
omni.kit.commands.execute('CreateNode',
    usd_context_name='',
    graph_path='/ActionGraph',
    node_type='omni.graph.nodes.ConstantDouble',
    node_name='MyConstant'
)
```

### 3. **节点属性操作**
```python
# 获取属性
attr = node.get_attribute("inputs:value")

# 设置属性值
attr.set(42.0)

# 创建新属性
node.create_attribute("inputs:custom", og.Type(og.BaseDataType.DOUBLE))
```

## 实际使用步骤

### 步骤1: 在Isaac Sim中运行脚本

#### 方法A: Script Editor
1. 打开Isaac Sim
2. 菜单栏 → **Window** → **Script Editor**
3. 将Python代码粘贴到编辑器中
4. 点击 **Run** 按钮执行

#### 方法B: 扩展方式
1. 创建自定义扩展
2. 在扩展的startup()函数中调用脚本
3. 通过Extension Manager加载

#### 方法C: 命令行执行
```bash
# 在Isaac Sim安装目录下
./isaac-sim.sh --exec "path/to/your/script.py"
```

### 步骤2: 使用提供的脚本

#### 快速开始 - 使用简化版本
```python
# 在Isaac Sim Script Editor中运行
exec(open('isaac_sim_launcher.py').read())
```

#### 完整版本 - 使用所有脚本模块
```python
# 在Isaac Sim Script Editor中运行
exec(open('underwater_action_graph.py').read())
```

### 步骤3: 验证和调试

#### 检查Action Graph
1. 打开 **Window** → **Visual Scripting** → **Action Graph**
2. 在左侧面板选择创建的图
3. 查看节点连接和参数设置

#### 运行仿真
1. 点击播放按钮 ▶️
2. 观察ROV的物理行为
3. 检查控制台输出的日志信息

## 常用节点类型

### 1. **输入节点**
```python
# 游戏手柄输入
'omni.graph.nodes.GamepadInput'

# 键盘输入
'omni.graph.nodes.KeyboardInput'

# 时间输入
'omni.graph.action.OnTick'
```

### 2. **数据获取节点**
```python
# 获取Transform
'omni.graph.nodes.GetPrimLocalToWorldTransform'

# 获取属性
'omni.graph.nodes.GetPrimAttribute'

# 获取物理属性
'omni.graph.nodes.GetRigidBodyProperties'
```

### 3. **数学运算节点**
```python
# 向量数学
'omni.graph.nodes.VectorMath'

# 数组操作
'omni.graph.nodes.MakeArray'

# 分量提取
'omni.graph.nodes.ExtractComponent'
```

### 4. **物理应用节点**
```python
# 施加力
'omni.graph.nodes.ApplyForce'

# 设置刚体属性
'omni.graph.nodes.SetRigidBodyProperties'

# 设置Transform
'omni.graph.nodes.SetPrimLocalToWorldTransform'
```

### 5. **Python脚本节点**
```python
# 自定义Python脚本
'omni.graph.scriptnode.ScriptNode'
```

## 高级技巧

### 1. **动态节点创建**
```python
def create_thruster_nodes(self, thruster_count=4):
    """动态创建多个推进器节点"""
    for i in range(thruster_count):
        node_name = f"Thruster_{i}"
        self.add_apply_force_node(node_name, f"/World/ROV/Thruster_{i}")
```

### 2. **条件图结构**
```python
def create_conditional_graph(self, use_pid_control=True):
    """根据条件创建不同的图结构"""
    if use_pid_control:
        self.add_script_node("PIDController", "scripts/controller.py", ...)
    else:
        self.add_script_node("SimpleController", "scripts/simple_control.py", ...)
```

### 3. **参数化配置**
```python
class GraphConfig:
    def __init__(self):
        self.rov_mass = 1000.0
        self.water_density = 1025.0
        self.max_thrust = 500.0
        
def build_graph_with_config(config):
    # 使用配置参数创建图
    pass
```

### 4. **错误处理和日志**
```python
import carb

def safe_connect_nodes(self, source, target):
    try:
        og.Controller.connect(source, target)
        carb.log_info(f"连接成功: {source} -> {target}")
    except Exception as e:
        carb.log_error(f"连接失败: {e}")
```

## 调试技巧

### 1. **添加调试输出**
```python
# 添加Print节点输出中间值
self.add_print_node("DebugPosition", "Position: ")
```

### 2. **使用Graph Variables**
```python
# 存储中间计算结果
self.add_graph_variable("current_depth", og.Type(og.BaseDataType.DOUBLE))
```

### 3. **性能监控**
```python
# 添加性能监控节点
self.add_profiler_node("BuoyancyProfiler")
```

## 最佳实践

### 1. **模块化设计**
- 将不同功能的节点组织成独立的方法
- 使用类来封装相关的节点和连接
- 支持配置参数和选项

### 2. **错误处理**
- 检查节点创建是否成功
- 验证属性连接
- 提供有意义的错误信息

### 3. **文档和注释**
- 为每个节点添加清晰的注释
- 说明数据流和计算逻辑
- 提供使用示例

### 4. **测试和验证**
- 创建单元测试验证节点功能
- 使用已知输入测试输出
- 验证物理行为的合理性

## 常见问题解决

### 问题1: 节点创建失败
```python
# 检查节点类型是否正确
available_types = og.get_node_types()
print("可用节点类型:", available_types)
```

### 问题2: 属性连接错误
```python
# 检查属性类型匹配
source_type = source_attr.get_type()
target_type = target_attr.get_type()
print(f"类型匹配: {source_type} -> {target_type}")
```

### 问题3: 脚本执行错误
```python
# 在脚本中添加错误处理
def compute(db):
    try:
        # 计算逻辑
        pass
    except Exception as e:
        print(f"脚本执行错误: {e}")
```

## 总结

使用Python API创建Action Graph的优势：
- **灵活性**: 动态创建复杂的图结构
- **可重复性**: 代码化配置便于重用
- **可维护性**: 版本控制和团队协作
- **可扩展性**: 易于添加新功能和节点

通过掌握这些技巧，您可以高效地创建复杂的水下仿真系统，并根据需要进行定制和扩展。
