"""
Isaac Sim 4.5 Simple Test Script
Test basic Action Graph Python API functionality using Controller API
"""

import omni.graph.core as og
import omni.kit.commands
import carb


def test_controller_api():
    """Test the OmniGraph Controller API"""
    graph_path = "/TestActionGraph"
    
    try:
        # Delete existing graph
        omni.kit.commands.execute('DeletePrims', paths=[graph_path])
        carb.log_info("Deleted existing graph")
    except:
        pass
    
    try:
        # Create Action Graph using Controller API
        keys = og.Controller.Keys
        (graph, nodes_created, _, _) = og.Controller.edit(
            {"graph_path": graph_path, "evaluator_name": "execution"},
            {
                keys.CREATE_NODES: [
                    ("ActionGraph", "omni.graph.action.ActionGraph"),
                    ("OnTick", "omni.graph.action.OnTick"),
                    ("ConstantDouble", "omni.graph.nodes.ConstantDouble"),
                    ("Print", "omni.graph.nodes.Print"),
                ],
            },
        )
        
        if graph and len(nodes_created) == 4:
            carb.log_info(f"Successfully created graph with {len(nodes_created)} nodes")
            
            # Get node references
            action_graph_node = nodes_created[0]
            on_tick_node = nodes_created[1]
            constant_node = nodes_created[2]
            print_node = nodes_created[3]
            
            # Set constant value
            og.Controller.edit(
                graph,
                {
                    keys.SET_VALUES: [
                        (constant_node.get_attribute("inputs:value"), 42.0),
                    ],
                },
            )
            
            # Connect nodes
            og.Controller.edit(
                graph,
                {
                    keys.CONNECT: [
                        (on_tick_node.get_attribute("outputs:tick"), 
                         print_node.get_attribute("inputs:execIn")),
                        (constant_node.get_attribute("outputs:value"), 
                         print_node.get_attribute("inputs:value")),
                    ],
                },
            )
            
            carb.log_info("Successfully connected nodes")
            return True
        else:
            carb.log_error("Failed to create all nodes")
            return False
            
    except Exception as e:
        carb.log_error(f"Error in test: {e}")
        return False


def test_script_node():
    """Test Python script node creation"""
    graph_path = "/TestScriptGraph"
    
    try:
        # Delete existing graph
        omni.kit.commands.execute('DeletePrims', paths=[graph_path])
    except:
        pass
    
    try:
        keys = og.Controller.Keys
        (graph, nodes_created, _, _) = og.Controller.edit(
            {"graph_path": graph_path, "evaluator_name": "execution"},
            {
                keys.CREATE_NODES: [
                    ("ActionGraph", "omni.graph.action.ActionGraph"),
                    ("OnTick", "omni.graph.action.OnTick"),
                    ("ScriptNode", "omni.graph.scriptnode.ScriptNode"),
                ],
            },
        )
        
        if graph and len(nodes_created) == 3:
            script_node = nodes_created[2]
            
            # Set script content
            script_content = '''
def setup(db):
    db.counter = 0
    print("Script node initialized")

def compute(db):
    db.counter += 1
    db.outputs.result = db.counter * 2.0
    if db.counter <= 3:
        print(f"Script execution #{db.counter}, result: {db.outputs.result}")
'''
            
            og.Controller.edit(
                graph,
                {
                    keys.SET_VALUES: [
                        (script_node.get_attribute("inputs:script"), script_content),
                    ],
                },
            )
            
            carb.log_info("Successfully created script node")
            return True
        else:
            carb.log_error("Failed to create script nodes")
            return False
            
    except Exception as e:
        carb.log_error(f"Error creating script node: {e}")
        return False


def main():
    """Main test function"""
    print("=" * 60)
    print("Isaac Sim 4.5 Action Graph Controller API Test")
    print("=" * 60)
    
    # Test basic Controller API
    print("\n1. Testing basic Controller API...")
    if test_controller_api():
        print("✓ Basic Controller API test passed")
    else:
        print("✗ Basic Controller API test failed")
        return
    
    # Test script node
    print("\n2. Testing script node creation...")
    if test_script_node():
        print("✓ Script node test passed")
    else:
        print("✗ Script node test failed")
        return
    
    print("\n" + "=" * 60)
    print("All tests completed successfully!")
    print("=" * 60)
    print("Next steps:")
    print("1. Open Window → Visual Scripting → Action Graph")
    print("2. Select /TestActionGraph or /TestScriptGraph")
    print("3. Click Play button to see execution")
    print("4. Check console output for script messages")
    print("5. Run the full underwater simulation:")
    print("   exec(open('underwater_action_graph_en.py').read())")


if __name__ == "__main__":
    main()
