# Isaac Sim 水下仿真脚本集成指南

## 概述
本指南详细说明如何在Isaac Sim 4.5.0中应用scripts目录下的Python脚本来实现水下机器人仿真。

## 脚本功能概览

### 1. quat_to_euler.py - 姿态转换
**功能**: 将四元数转换为欧拉角
**输入**: 
- `quaternion`: [x, y, z, w] 四元数数组
**输出**: 
- `rotation`: [roll, pitch, yaw] 欧拉角(度)

### 2. buoyancy_forces.py - 浮力计算
**功能**: 计算考虑旋转的三维浮力
**输入**:
- `volume`: 物体体积 (m³)
- `height`: 物体高度 (m)
- `z_position`: Z轴位置 (m)
- `rotation`: 旋转角度 [roll, pitch, yaw] (度)
**输出**:
- `x_force`, `y_force`, `z_force`: 三维浮力分量 (N)

### 3. damping.py - 阻尼计算
**功能**: 根据浸没深度计算阻尼系数
**输入**:
- `z_position`: Z轴位置 (m)
- `max_damping`: 最大阻尼系数
- `floating_obj_height`: 物体高度 (m)
**输出**:
- `linear_damping`: 线性阻尼系数
- `angular_damping`: 角度阻尼系数

### 4. controller.py - PID控制
**功能**: 姿态稳定控制
**输入**:
- `orientation`: 当前姿态角度
- `dive_force`: 潜水力
**输出**:
- `force`: 正向控制力 [x,y,z]
- `minus_force`: 反向控制力 [x,y,z]

### 5. linear_angular_control.py - 推进器控制
**功能**: 四推进器运动控制
**输入**:
- `y_stick`: Y轴操纵杆值
- `x_stick`: X轴操纵杆值
**输出**:
- `left_front`, `right_front`, `left_back`, `right_back`: 各推进器力向量

## Isaac Sim中的集成步骤

### 步骤1: 打开场景文件
```bash
# 在Isaac Sim中打开以下场景文件之一：
# - BUOYANCY_TEST.usd (浮力测试)
# - ROV_TEST.usd (ROV基础测试)
# - ROV_THRUSTERS.usd (ROV推进器测试)
```

### 步骤2: 配置Action Graph
1. 打开 **Window > Visual Scripting > Action Graph**
2. 在Action Graph中添加Python Script节点
3. 为每个脚本创建对应的节点

### 步骤3: 连接数据流
典型的数据流连接：

```
物体姿态(四元数) → quat_to_euler.py → 欧拉角
                                    ↓
物体位置 + 欧拉角 → buoyancy_forces.py → 浮力向量
                                    ↓
物体位置 → damping.py → 阻尼系数 → 物理属性设置
                                    ↓
操纵杆输入 → linear_angular_control.py → 推进器力
                                    ↓
姿态角度 → controller.py → 稳定控制力 → 最终力输出
```

### 步骤4: 参数配置
在Action Graph中为每个脚本节点配置输入参数：

**物体属性参数**:
- 体积: 1.0 m³ (根据实际模型调整)
- 高度: 2.0 m (根据实际模型调整)
- 最大阻尼: 10.0

**控制参数**:
- PID增益: Kp=100, Ki=10, Kd=0.01
- 控制饱和限制: ±1000

## 实际应用示例

### 浮体仿真 (BUOYANCY_TEST.usd)
1. 使用 `buoyancy_forces.py` 计算浮力
2. 使用 `damping.py` 计算阻尼
3. 将力应用到物体的刚体组件

### ROV控制仿真 (ROV_THRUSTERS.usd)
1. 使用 `quat_to_euler.py` 获取姿态角度
2. 使用 `controller.py` 进行姿态稳定
3. 使用 `linear_angular_control.py` 处理操纵杆输入
4. 使用 `buoyancy_forces.py` 计算浮力
5. 将所有力合成后应用到ROV

### 控制器连接
- PS4手柄输入 → Gamepad节点 → linear_angular_control.py
- 物体姿态 → quat_to_euler.py → controller.py
- 最终力输出 → Apply Forces节点 → 物体刚体

## 调试和优化建议

### 1. 参数调整
- 根据实际物体尺寸调整体积和高度参数
- 根据仿真效果调整PID参数
- 根据期望的运动特性调整阻尼系数

### 2. 性能优化
- 确保脚本在每帧都能正常执行
- 监控计算性能，避免复杂计算影响实时性
- 使用Isaac Sim的性能分析工具检查瓶颈

### 3. 物理真实性
- 调整水密度参数以匹配实际环境
- 考虑添加水流、波浪等环境因素
- 验证浮力计算的准确性

## 常见问题解决

### 问题1: 脚本无法加载
- 确保脚本路径正确
- 检查Python语法错误
- 验证Isaac Sim版本兼容性

### 问题2: 数据连接错误
- 检查输入输出数据类型匹配
- 确认节点连接正确
- 使用Action Graph的调试功能

### 问题3: 物理行为异常
- 检查物理参数设置
- 验证力的方向和大小
- 调整时间步长和求解器参数
