"""
浮力控制模块
提供计算物体浮力的功能，用于水下机器人或浮体的浮力仿真
"""

def setup(db):
    """
    初始化函数
    设置必要的变量或数据结构

    参数:
        db: 数据库对象，用于存储和传递数据

    注意:
        此模块不需要特殊的初始化操作
    """
    pass  # 此情况下不需要初始化操作

def compute(db):
    """
    计算物体的浮力（非旋转浮力）

    根据阿基米德原理计算浮力：F = ρ * V_submerged * g
    其中 ρ 是流体密度，V_submerged 是浸没体积，g 是重力加速度

    参数:
        db: 数据库对象，包含输入和输出数据
            输入参数:
                - Volume: 物体总体积 (m³)
                - height: 物体高度 (m)
                - z_position: 物体在Z轴的位置 (m)
            输出参数:
                - z_force: 计算得到的Z方向浮力 (N)

    返回:
        无直接返回值，结果存储在 db.outputs.z_force 中
    """
    # 定义物理常数
    water_density = 1  # 水的密度 kg/m³ (简化值，实际海水密度约1025 kg/m³)
    gravity = 9.8  # 重力加速度 m/s²

    # 获取输入参数
    volume = db.inputs.Volume  # 物体总体积 m³
    height = db.inputs.height  # 物体高度 m
    z_position = db.inputs.z_position  # 物体Z轴位置 m

    # 计算浸没体积
    submerged_height = calculate_submerged_height(z_position, height)  # 计算浸没高度
    submerged_volume = volume * submerged_height  # 浸没体积 = 总体积 × 浸没高度比例

    # 根据阿基米德原理计算浮力
    buoyancy_force = water_density * submerged_volume * gravity

    # 将计算结果输出到数据库
    db.outputs.z_force = buoyancy_force

def calculate_submerged_height(z_position, height):
    """
    根据物体的Z轴位置计算浸没高度比例

    假设水面位于Z=0处，物体中心在z_position位置

    参数:
        z_position: 物体中心的Z轴位置 (m)
                   正值表示在水面上方，负值表示在水面下方
        height: 物体的总高度 (m)

    返回:
        float: 浸没高度比例 (0.0 到 1.0)
               0.0 表示完全在水面上方
               1.0 表示完全浸没

    计算逻辑:
        - 如果物体底部在水面上方：浸没高度 = 0
        - 如果物体顶部在水面下方：浸没高度 = 物体总高度
        - 如果物体部分浸没：浸没高度 = 水面下方的部分高度
    """
    center_of_h = height / 2  # 物体中心到顶部/底部的距离

    if z_position >= center_of_h:
        # 物体完全在水面上方（物体底部在水面上方或刚好接触水面）
        return 0.0
    elif z_position < -center_of_h:
        # 物体完全在水面下方（物体顶部在水面下方）
        return height
    else:
        # 物体部分浸没，计算浸没的高度
        # center_of_h - z_position 表示从物体中心到水面的距离加上物体半高
        return center_of_h - z_position
