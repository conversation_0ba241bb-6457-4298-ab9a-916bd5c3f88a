"""
Run Simple Multi-ROV System
Fixed version that avoids API checking issues
"""

print("🤖 Loading Simple Multi-ROV System...")
exec(open('simple_multi_rov_system.py').read())

print("\n" + "="*80)
print("🚀 LAUNCHING SIMPLE 3-ROV UNDERWATER SYSTEM")
print("="*80)

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 FIXED 3-ROV SYSTEM READY!")
        print("\n" + "="*70)
        print("🤖 ALL 3 ROVS NOW INCLUDED!")
        print("="*70)
        
        print("\n✅ Fixed Issues:")
        print("  • Removed problematic API checking")
        print("  • Simplified physics setup")
        print("  • All 3 ROVs now have individual physics")
        print("  • No more API conversion errors")
        
        print("\n🤖 Your Complete ROV Fleet:")
        print("  🔴 ROV_Original (/World/ROV): 800kg, -1.5m target")
        print("  🔵 ROV_Main (/World/ROV_Main): 1000kg, -4.0m target")
        print("  🟢 ROV_Scout (/World/ROV_Scout): 500kg, -2.0m target")
        
        print("\n📊 What You'll See After PLAY:")
        print("  • Individual physics for each ROV every 3 seconds")
        print("  • Environmental updates every 6 seconds")
        print("  • System status every 4 seconds")
        print("  • All 3 ROVs working independently")
        
        print("\n🎯 Expected Console Output:")
        print("  'ROV_Original: depth=X.XXm, buoyancy=XXXN, control=XXXN, drag=XXXN'")
        print("  'ROV_Main: depth=X.XXm, buoyancy=XXXN, control=XXXN, drag=XXXN'")
        print("  'ROV_Scout: depth=X.XXm, buoyancy=XXXN, control=XXXN, drag=XXXN'")
        print("  '=== Multi-ROV System Status (t=X.Xs, fps=XX.X) ==='")
        
        print("\n" + "="*70)
        print("🚀 ALL 3 ROVS READY FOR SIMULATION!")
        print("="*70)
        print("Your third ROV is now included in the physics system!")
        print("Click PLAY to see all 3 ROVs in action!")
        
    else:
        print("\n❌ SYSTEM SETUP FAILED")
        print("Check error messages above for troubleshooting.")
        print("This simplified version should avoid API issues.")
