"""
Minimal Underwater Simulation for Isaac Sim 4.5
Simple buoyancy calculation using working API patterns
"""

import omni.graph.core as og
import omni.kit.commands
import carb


class MinimalUnderwaterSim:
    """Minimal underwater simulation using proven API patterns"""
    
    def __init__(self, graph_path="/UnderwaterGraph"):
        self.graph_path = graph_path
        self.graph = None
        
    def create_simulation_graph(self, rov_prim_path="/World/ROV"):
        """Create minimal underwater simulation graph"""
        carb.log_info("Creating minimal underwater simulation...")
        
        # Delete existing graph
        try:
            omni.kit.commands.execute('DeletePrims', paths=[self.graph_path])
        except:
            pass
        
        try:
            # Create graph
            self.graph = og.Controller.create_graph({
                "graph_path": self.graph_path, 
                "evaluator_name": "execution"
            })
            
            if not self.graph:
                carb.log_error("Failed to create graph")
                return False
            
            carb.log_info("Graph created successfully")
            
            # Create nodes
            keys = og.Controller.Keys
            (_, nodes_created, _, _) = og.Controller.edit(
                self.graph,
                {
                    keys.CREATE_NODES: [
                        ("OnTick", "omni.graph.action.OnTick"),
                        ("GetTransform", "omni.graph.nodes.GetPrimLocalToWorldTransform"),
                        ("ExtractZ", "omni.graph.nodes.ExtractComponent"),
                        ("BuoyancyScript", "omni.graph.scriptnode.ScriptNode"),
                        ("ApplyForce", "omni.graph.nodes.ApplyForce"),
                    ],
                },
            )
            
            if len(nodes_created) != 5:
                carb.log_error(f"Expected 5 nodes, got {len(nodes_created)}")
                return False
            
            carb.log_info("Nodes created successfully")
            
            # Get node references
            on_tick = nodes_created[0]
            get_transform = nodes_created[1]
            extract_z = nodes_created[2]
            buoyancy_script = nodes_created[3]
            apply_force = nodes_created[4]
            
            # Configure nodes
            og.Controller.edit(
                self.graph,
                {
                    keys.SET_VALUES: [
                        # Set target prim for transform
                        (get_transform.get_attribute("inputs:prim"), rov_prim_path),
                        # Set extract component index (Z = 2)
                        (extract_z.get_attribute("inputs:index"), 2),
                        # Set target prim for force application
                        (apply_force.get_attribute("inputs:prim"), rov_prim_path),
                        # Set buoyancy script
                        (buoyancy_script.get_attribute("inputs:script"), self.get_buoyancy_script()),
                    ],
                },
            )
            
            carb.log_info("Node attributes configured")
            
            # Connect nodes
            og.Controller.edit(
                self.graph,
                {
                    keys.CONNECT: [
                        # Execution flow
                        (on_tick.get_attribute("outputs:tick"), 
                         get_transform.get_attribute("inputs:execIn")),
                        
                        # Data flow
                        (get_transform.get_attribute("outputs:translation"), 
                         extract_z.get_attribute("inputs:vector")),
                        (extract_z.get_attribute("outputs:component"), 
                         buoyancy_script.get_attribute("inputs:z_position")),
                        (buoyancy_script.get_attribute("outputs:force"), 
                         apply_force.get_attribute("inputs:force")),
                    ],
                },
            )
            
            carb.log_info("Nodes connected successfully")
            carb.log_info("Minimal underwater simulation created!")
            return True
            
        except Exception as e:
            carb.log_error(f"Error creating simulation: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_buoyancy_script(self):
        """Get the buoyancy calculation script"""
        return '''
def setup(db):
    # ROV properties
    db.volume = 8.0  # 2x2x2 cube volume in m³
    db.height = 2.0  # ROV height in meters
    db.water_density = 1000.0  # kg/m³
    db.gravity = 9.8  # m/s²
    db.execution_count = 0
    print("Buoyancy script initialized")

def compute(db):
    db.execution_count += 1
    
    # Get Z position (depth)
    z_pos = db.inputs.z_position
    
    # Calculate buoyancy force
    if z_pos < 0:  # Below water surface (negative Z)
        # Calculate submerged volume
        depth = abs(z_pos)
        submerged_height = min(db.height, depth)
        submerged_ratio = submerged_height / db.height
        submerged_volume = db.volume * submerged_ratio
        
        # Calculate buoyancy force (upward)
        buoyancy_force = db.water_density * submerged_volume * db.gravity
        
        # Apply force in Z direction (upward)
        force_vector = [0.0, 0.0, buoyancy_force]
        
        # Debug output (limited)
        if db.execution_count % 60 == 0:  # Every 60 frames (~1 second)
            print(f"Depth: {depth:.2f}m, Submerged: {submerged_ratio:.2f}, Force: {buoyancy_force:.1f}N")
        
    else:  # Above water surface
        force_vector = [0.0, 0.0, 0.0]
        
        if db.execution_count % 60 == 0:
            print(f"Above water at Z: {z_pos:.2f}m")
    
    # Output the force vector
    db.outputs.force = force_vector
'''


def create_simple_rov():
    """Create a simple ROV for testing"""
    rov_path = "/World/ROV"
    
    try:
        # Delete existing ROV
        omni.kit.commands.execute('DeletePrims', paths=[rov_path])
    except:
        pass
    
    try:
        # Create ROV cube
        omni.kit.commands.execute('CreateMeshPrimWithDefaultXform',
            prim_type='Cube',
            prim_path=rov_path
        )
        
        # Set position slightly below water surface for testing
        omni.kit.commands.execute('TransformPrimSRT',
            path=rov_path,
            new_translation=(0.0, 0.0, -1.0)  # 1 meter below surface
        )
        
        carb.log_info(f"Created simple ROV at {rov_path}")
        return True
        
    except Exception as e:
        carb.log_error(f"Error creating ROV: {e}")
        return False


def main():
    """Main function"""
    print("=" * 60)
    print("Minimal Underwater Simulation Setup")
    print("=" * 60)
    
    # Create simple ROV
    print("\n1. Creating simple ROV...")
    if create_simple_rov():
        print("✓ ROV created successfully")
    else:
        print("✗ Failed to create ROV")
        return
    
    # Create simulation graph
    print("\n2. Creating underwater simulation graph...")
    sim = MinimalUnderwaterSim()
    if sim.create_simulation_graph("/World/ROV"):
        print("✓ Simulation graph created successfully")
    else:
        print("✗ Failed to create simulation graph")
        return
    
    print("\n" + "=" * 60)
    print("Setup Complete!")
    print("=" * 60)
    print("Usage:")
    print("1. Click Play button to start simulation")
    print("2. Open Window → Visual Scripting → Action Graph")
    print("3. Select /UnderwaterGraph to see the nodes")
    print("4. Move ROV up/down to see buoyancy effects")
    print("5. Check console for debug messages")
    print()
    print("The ROV should experience upward buoyancy force when underwater!")


if __name__ == "__main__":
    main()
